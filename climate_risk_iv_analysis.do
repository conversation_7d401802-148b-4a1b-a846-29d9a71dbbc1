/*==============================================================================
* 文件名: climate_risk_iv_analysis.do
* 研究主题: 中国的公众气候风险表达对上市公司碳排放强度的影响
* 作者: [您的姓名]
* 日期: 2025年1月
* 数据: alldata9.dta (2011-2022年面板数据)
*==============================================================================*/

clear all
set more off
set matsize 10000

* 设置工作目录和数据路径
cd "/Users/<USER>/Desktop/clitansz"

* 加载数据
use "alldata9.dta", clear

/*==============================================================================
* 第一部分：数据预处理和基本设置
*==============================================================================*/

* 设置面板数据结构
xtset stockCode year

* 生成基本变量标签（中文注释）
label variable stockCode "股票代码"
label variable year "年份"
label variable climateRiskPublicView "公众气候风险表达指数"
label variable carbonIntensityRegional "碳排放强度"
label variable industryCode "行业代码"
label variable Prvcnm_id "省份代码"
label variable CITYCODE "城市代码"

* 检查关键变量的缺失值情况
summarize climateRiskPublicView carbonIntensityRegional industryCode Prvcnm_id CITYCODE

/*==============================================================================
* 第二部分：构建地理/区域聚合工具变量
*==============================================================================*/

* 1. 同年度同城市其他企业的公众气候风险表达均值
bysort CITYCODE year: egen temp_city_mean = mean(climateRiskPublicView)
bysort CITYCODE year: egen temp_city_count = count(climateRiskPublicView)
gen iv_city_other_mean = (temp_city_mean * temp_city_count - climateRiskPublicView) / (temp_city_count - 1) if temp_city_count > 1
replace iv_city_other_mean = . if temp_city_count <= 1
drop temp_city_mean temp_city_count
label variable iv_city_other_mean "同城市其他企业气候风险表达均值"

* 2. 同年度同省份其他企业的公众气候风险表达均值  
bysort Prvcnm_id year: egen temp_prov_mean = mean(climateRiskPublicView)
bysort Prvcnm_id year: egen temp_prov_count = count(climateRiskPublicView)
gen iv_prov_other_mean = (temp_prov_mean * temp_prov_count - climateRiskPublicView) / (temp_prov_count - 1) if temp_prov_count > 1
replace iv_prov_other_mean = . if temp_prov_count <= 1
drop temp_prov_mean temp_prov_count
label variable iv_prov_other_mean "同省份其他企业气候风险表达均值"

* 3. 同年度同城市同行业其他企业的公众气候风险表达均值
bysort CITYCODE industryCode year: egen temp_city_ind_mean = mean(climateRiskPublicView)
bysort CITYCODE industryCode year: egen temp_city_ind_count = count(climateRiskPublicView)
gen iv_city_ind_other_mean = (temp_city_ind_mean * temp_city_ind_count - climateRiskPublicView) / (temp_city_ind_count - 1) if temp_city_ind_count > 1
replace iv_city_ind_other_mean = . if temp_city_ind_count <= 1
drop temp_city_ind_mean temp_city_ind_count
label variable iv_city_ind_other_mean "同城市同行业其他企业气候风险表达均值"

* 4. 同年度同省份同行业其他企业的公众气候风险表达均值
bysort Prvcnm_id industryCode year: egen temp_prov_ind_mean = mean(climateRiskPublicView)
bysort Prvcnm_id industryCode year: egen temp_prov_ind_count = count(climateRiskPublicView)
gen iv_prov_ind_other_mean = (temp_prov_ind_mean * temp_prov_ind_count - climateRiskPublicView) / (temp_prov_ind_count - 1) if temp_prov_ind_count > 1
replace iv_prov_ind_other_mean = . if temp_prov_ind_count <= 1
drop temp_prov_ind_mean temp_prov_ind_count
label variable iv_prov_ind_other_mean "同省份同行业其他企业气候风险表达均值"

/*==============================================================================
* 第三部分：构建行业聚合工具变量
*==============================================================================*/

* 5. 同年度同行业其他企业的公众气候风险表达均值（全国层面）
bysort industryCode year: egen temp_ind_mean = mean(climateRiskPublicView)
bysort industryCode year: egen temp_ind_count = count(climateRiskPublicView)
gen iv_ind_other_mean = (temp_ind_mean * temp_ind_count - climateRiskPublicView) / (temp_ind_count - 1) if temp_ind_count > 1
replace iv_ind_other_mean = . if temp_ind_count <= 1
drop temp_ind_mean temp_ind_count
label variable iv_ind_other_mean "同行业其他企业气候风险表达均值"

* 6. 同年度同行业不同省份企业的公众气候风险表达均值
bysort industryCode Prvcnm_id year: egen temp_ind_prov_mean = mean(climateRiskPublicView)
bysort industryCode year: egen temp_ind_total_mean = mean(climateRiskPublicView)
bysort industryCode Prvcnm_id year: egen temp_ind_prov_count = count(climateRiskPublicView)
bysort industryCode year: egen temp_ind_total_count = count(climateRiskPublicView)
gen iv_ind_other_prov_mean = (temp_ind_total_mean * temp_ind_total_count - temp_ind_prov_mean * temp_ind_prov_count) / (temp_ind_total_count - temp_ind_prov_count) if temp_ind_total_count > temp_ind_prov_count
replace iv_ind_other_prov_mean = . if temp_ind_total_count <= temp_ind_prov_count
drop temp_ind_prov_mean temp_ind_total_mean temp_ind_prov_count temp_ind_total_count
label variable iv_ind_other_prov_mean "同行业不同省份企业气候风险表达均值"

/*==============================================================================
* 第四部分：构建滞后期工具变量
*==============================================================================*/

* 7. 公众气候风险表达滞后1期
gen iv_climate_lag1 = L.climateRiskPublicView
label variable iv_climate_lag1 "公众气候风险表达滞后1期"

* 8. 公众气候风险表达滞后2期
gen iv_climate_lag2 = L2.climateRiskPublicView
label variable iv_climate_lag2 "公众气候风险表达滞后2期"

* 9. 公众气候风险表达滞后3期
gen iv_climate_lag3 = L3.climateRiskPublicView
label variable iv_climate_lag3 "公众气候风险表达滞后3期"

* 10. 企业历史气候风险表达均值（2011-2013年基期）
bysort stockCode: egen temp_hist_mean = mean(climateRiskPublicView) if year >= 2011 & year <= 2013
bysort stockCode: egen iv_hist_mean = max(temp_hist_mean)
replace iv_hist_mean = . if year <= 2013  // 只在2014年之后使用
drop temp_hist_mean
label variable iv_hist_mean "企业历史气候风险表达均值(2011-2013)"

* 11. 城市历史气候风险表达均值（2011-2013年基期）
bysort CITYCODE: egen temp_city_hist_mean = mean(climateRiskPublicView) if year >= 2011 & year <= 2013
bysort CITYCODE: egen iv_city_hist_mean = max(temp_city_hist_mean)
replace iv_city_hist_mean = . if year <= 2013
drop temp_city_hist_mean
label variable iv_city_hist_mean "城市历史气候风险表达均值(2011-2013)"

/*==============================================================================
* 第五部分：定义控制变量
*==============================================================================*/

* 企业层面控制变量
global firm_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity"

* 地区层面控制变量  
global region_controls "secondaryIndustryRatio gdpPerCapita"

* 所有控制变量
global all_controls "$firm_controls $region_controls"

/*==============================================================================
* 第六部分：描述性统计
*==============================================================================*/

* 主要变量描述性统计
summarize climateRiskPublicView carbonIntensityRegional $all_controls

* 工具变量描述性统计
summarize iv_city_other_mean iv_prov_other_mean iv_ind_other_mean iv_climate_lag1 iv_climate_lag2 iv_hist_mean

* 工具变量与内生变量的相关性检验
pwcorr climateRiskPublicView iv_city_other_mean iv_prov_other_mean iv_ind_other_mean iv_climate_lag1 iv_climate_lag2, star(0.01) sig

/*==============================================================================
* 第七部分：基准OLS回归（用于对比）
*==============================================================================*/

* OLS回归
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store ols_baseline
esttab ols_baseline, b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("基准OLS回归结果") mtitles("碳排放强度")

/*==============================================================================
* 第八部分：工具变量回归分析
*==============================================================================*/

* 8.1 使用同城市其他企业均值作为工具变量
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_city
estat firststage  // 第一阶段F统计量检验
estat overid     // 过度识别检验（如果有多个工具变量）

* 8.2 使用同省份其他企业均值作为工具变量
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_prov_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_prov
estat firststage
estat overid

* 8.3 使用同行业其他企业均值作为工具变量
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_ind_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_ind
estat firststage
estat overid

* 8.4 使用滞后期作为工具变量
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_climate_lag2), ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_lag2
estat firststage
estat overid

* 8.5 使用历史均值作为工具变量
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_hist_mean) if year >= 2014, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_hist
estat firststage
estat overid

* 8.6 使用多个工具变量的组合回归
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean iv_prov_other_mean iv_climate_lag2), ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_combined
estat firststage
estat overid

/*==============================================================================
* 第九部分：工具变量有效性检验
*==============================================================================*/

* 9.1 弱工具变量检验（Stock-Yogo临界值）
* 注：Stata会自动报告Kleibergen-Paap rk Wald F统计量

* 9.2 内生性检验（Durbin-Wu-Hausman检验）
* 使用同城市其他企业均值作为工具变量进行内生性检验
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE)
estat endogenous  // 内生性检验

* 9.3 工具变量外生性检验（与因变量的直接相关性）
reghdfe carbonIntensityRegional iv_city_other_mean $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
test iv_city_other_mean  // 检验工具变量是否直接影响因变量

reghdfe carbonIntensityRegional iv_prov_other_mean $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
test iv_prov_other_mean

reghdfe carbonIntensityRegional iv_ind_other_mean $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
test iv_ind_other_mean

/*==============================================================================
* 第十部分：结果输出和对比
*==============================================================================*/

* 10.1 主要回归结果对比表
esttab ols_baseline iv_city iv_prov iv_ind iv_lag2 iv_combined, ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("公众气候风险表达对碳排放强度的影响：OLS vs IV估计") ///
    mtitles("OLS" "IV-城市" "IV-省份" "IV-行业" "IV-滞后" "IV-组合") ///
    scalars("N 观测值" "r2 R-squared" "F F统计量") ///
    addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
             "所有回归均控制企业和年份固定效应，标准误在城市层面聚类")

* 10.2 第一阶段回归结果（工具变量相关性）
* 重新运行主要的IV回归以获取第一阶段结果
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE) first savefirst
estimates restore _ivreg2_climateRiskPublicView  // 第一阶段结果
estimates store first_stage_city

ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_prov_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE) first savefirst
estimates restore _ivreg2_climateRiskPublicView
estimates store first_stage_prov

* 第一阶段结果对比
esttab first_stage_city first_stage_prov, ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("第一阶段回归结果：工具变量相关性检验") ///
    mtitles("城市工具变量" "省份工具变量") ///
    keep(iv_city_other_mean iv_prov_other_mean) ///
    scalars("N 观测值" "F F统计量")

/*==============================================================================
* 第十一部分：稳健性检验
*==============================================================================*/

* 11.1 更换聚类标准误（省份层面聚类）
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(Prvcnm_id) first
estimates store iv_city_prov_cluster

* 11.2 更换聚类标准误（行业层面聚类）
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(industryCode) first
estimates store iv_city_ind_cluster

* 11.3 双向聚类标准误（城市和年份）
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE year) first
estimates store iv_city_double_cluster

* 稳健性检验结果对比
esttab iv_city iv_city_prov_cluster iv_city_ind_cluster iv_city_double_cluster, ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("稳健性检验：不同聚类方式的IV估计结果") ///
    mtitles("城市聚类" "省份聚类" "行业聚类" "双向聚类") ///
    scalars("N 观测值")

/*==============================================================================
* 第十二部分：异质性分析
*==============================================================================*/

* 12.1 按企业所有制分组
* 国有企业
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if stateOwned == 1, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_soe

* 非国有企业
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if stateOwned == 0, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_nonsoe

* 12.2 按企业规模分组（以中位数为界）
summarize firmSize, detail
local median_size = r(p50)

* 大企业
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if firmSize >= `median_size', ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_large

* 小企业
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if firmSize < `median_size', ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store iv_small

* 异质性分析结果
esttab iv_soe iv_nonsoe iv_large iv_small, ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("异质性分析：不同企业特征下的IV估计结果") ///
    mtitles("国有企业" "非国有企业" "大企业" "小企业") ///
    scalars("N 观测值")

/*==============================================================================
* 第十三部分：保存结果和数据
*==============================================================================*/

* 保存包含工具变量的数据集
save "alldata9_with_iv.dta", replace

* 输出主要结果到Word格式表格
esttab ols_baseline iv_city iv_prov iv_combined using "regression_results.rtf", ///
    replace b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("公众气候风险表达对碳排放强度的影响") ///
    mtitles("OLS" "IV-城市" "IV-省份" "IV-组合") ///
    addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
             "所有回归均控制企业和年份固定效应，标准误在城市层面聚类")

display "分析完成！结果已保存到 regression_results.rtf 文件中"
display "数据文件已保存为 alldata9_with_iv.dta"
