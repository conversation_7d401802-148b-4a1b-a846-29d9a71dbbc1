/*==============================================================================
* 简化版工具变量测试
* 目的：确保程序能正常运行，专注于核心功能
*==============================================================================*/

clear all
set more off

* 设置工作目录
cd "/Users/<USER>/Desktop/clitansz"

* 加载数据并立即排序
use "alldata9.dta", clear
sort stockCode year

* 基本数据清理
winsor2 climateRiskPublicView, replace cuts(1 99)
winsor2 carbonIntensityRegional, replace cuts(1 99)
winsor2 firmSize, replace cuts(1 99)
winsor2 firmAge, replace cuts(1 99)
winsor2 leverage, replace cuts(1 99)
winsor2 ownershipBalance, replace cuts(1 99)
winsor2 tobinsQ1, replace cuts(1 99)
winsor2 returnOnEquity, replace cuts(1 99)
winsor2 secondaryIndustryRatio, replace cuts(1 99)
winsor2 gdpPerCapita, replace cuts(1 99)

* 删除关键变量缺失的观测
drop if missing(stockCode) | missing(year)
drop if missing(climateRiskPublicView) | missing(carbonIntensityRegional)
drop if missing(industryCode) | missing(Prvcnm_id) | missing(CITYCODE)
keep if year >= 2011 & year <= 2022

* 设置面板数据
sort stockCode year
xtset stockCode year

* 定义控制变量
global firm_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity"
global region_controls "secondaryIndustryRatio gdpPerCapita"
global all_controls "$firm_controls $region_controls"

* 构建工具变量
display "构建工具变量..."

* 1. 滞后工具变量（最可靠）
gen iv_climate_lag2 = L2.climateRiskPublicView
label variable iv_climate_lag2 "公众气候风险表达滞后2期"

* 2. 省份层面工具变量
bysort Prvcnm_id year: egen iv_prov_mean = mean(climateRiskPublicView)
label variable iv_prov_mean "省份层面气候风险表达均值"

* 3. 行业层面工具变量
bysort industryCode year: egen iv_ind_mean = mean(climateRiskPublicView)
label variable iv_ind_mean "行业层面气候风险表达均值"

* 检查工具变量有效性
display ""
display "=== 工具变量诊断 ==="
foreach iv in iv_climate_lag2 iv_prov_mean iv_ind_mean {
    count if !missing(`iv') & !missing(climateRiskPublicView)
    display "`iv' 有效观测数: " r(N)
}

* 基准OLS回归
display ""
display "=== 基准OLS回归 ==="
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store ols_baseline

* 工具变量回归
display ""
display "=== 工具变量回归 ==="

* 1. 滞后工具变量
count if !missing(iv_climate_lag2) & !missing(climateRiskPublicView) & !missing(carbonIntensityRegional)
if r(N) > 100 {
    display "使用滞后工具变量..."
    ivreghdfe carbonIntensityRegional $all_controls ///
        (climateRiskPublicView = iv_climate_lag2), ///
        absorb(stockCode year) cluster(CITYCODE) first
    estimates store iv_lag
    
    * 获取F统计量
    local f_lag = e(mF)
    if missing(`f_lag') {
        local f_lag = e(F_f)
    }
    display "滞后工具变量第一阶段F统计量: " `f_lag'
}
else {
    display "滞后工具变量观测数不足"
    local f_lag = .
}

* 2. 省份工具变量
count if !missing(iv_prov_mean) & !missing(climateRiskPublicView) & !missing(carbonIntensityRegional)
if r(N) > 100 {
    display "使用省份工具变量..."
    ivreghdfe carbonIntensityRegional $all_controls ///
        (climateRiskPublicView = iv_prov_mean), ///
        absorb(stockCode year) cluster(CITYCODE) first
    estimates store iv_prov
    
    * 获取F统计量
    local f_prov = e(mF)
    if missing(`f_prov') {
        local f_prov = e(F_f)
    }
    display "省份工具变量第一阶段F统计量: " `f_prov'
}
else {
    display "省份工具变量观测数不足"
    local f_prov = .
}

* 3. 行业工具变量
count if !missing(iv_ind_mean) & !missing(climateRiskPublicView) & !missing(carbonIntensityRegional)
if r(N) > 100 {
    display "使用行业工具变量..."
    ivreghdfe carbonIntensityRegional $all_controls ///
        (climateRiskPublicView = iv_ind_mean), ///
        absorb(stockCode year) cluster(CITYCODE) first
    estimates store iv_ind
    
    * 获取F统计量
    local f_ind = e(mF)
    if missing(`f_ind') {
        local f_ind = e(F_f)
    }
    display "行业工具变量第一阶段F统计量: " `f_ind'
}
else {
    display "行业工具变量观测数不足"
    local f_ind = .
}

* 工具变量有效性总结
display ""
display "=== 工具变量有效性总结 ==="
display "Stock-Yogo临界值: 16.38"

if !missing(`f_lag') {
    display "滞后工具变量F统计量: " `f_lag'
    if `f_lag' > 16.38 {
        display "✓ 滞后工具变量通过检验"
    }
    else {
        display "✗ 滞后工具变量未通过检验"
    }
}

if !missing(`f_prov') {
    display "省份工具变量F统计量: " `f_prov'
    if `f_prov' > 16.38 {
        display "✓ 省份工具变量通过检验"
    }
    else {
        display "✗ 省份工具变量未通过检验"
    }
}

if !missing(`f_ind') {
    display "行业工具变量F统计量: " `f_ind'
    if `f_ind' > 16.38 {
        display "✓ 行业工具变量通过检验"
    }
    else {
        display "✗ 行业工具变量未通过检验"
    }
}

* 输出结果
display ""
display "=== 输出结果 ==="

* 选择最佳工具变量结果输出
if !missing(`f_lag') {
    esttab ols_baseline iv_lag using "simple_results.rtf", replace ///
        b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
        title("公众气候风险表达对碳排放强度的影响") ///
        mtitles("OLS" "IV-滞后") ///
        addnotes("注：***p<0.01, **p<0.05, *p<0.1")
    display "结果已保存到 simple_results.rtf"
}
else if !missing(`f_prov') {
    esttab ols_baseline iv_prov using "simple_results.rtf", replace ///
        b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
        title("公众气候风险表达对碳排放强度的影响") ///
        mtitles("OLS" "IV-省份") ///
        addnotes("注：***p<0.01, **p<0.05, *p<0.1")
    display "结果已保存到 simple_results.rtf"
}
else {
    display "没有有效的工具变量结果"
}

display ""
display "简化测试完成！"
