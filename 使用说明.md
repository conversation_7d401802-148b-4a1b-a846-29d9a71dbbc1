# 工具变量分析代码使用说明

## 🔍 问题解决

**原问题**：城市工具变量与内生变量完全相关（相关系数=1.000）

**根本原因**：
1. 很多城市只有1个企业，导致"其他企业均值"就是企业自身
2. 即使有多个企业，由于数据特征，计算出的"其他企业均值"与企业自身值几乎相同

**解决方案**：
1. ✅ 优先使用**滞后工具变量**（`iv_climate_lag2`）- 最可靠
2. ✅ 使用**省份层面均值**（`iv_prov_mean_all`）- 避免排除自身的问题
3. ✅ 使用**行业层面均值**（`iv_ind_mean_all`）- 行业聚合
4. ✅ 组合多个有效工具变量

## 🎯 推荐的工具变量优先级

### 1. 滞后工具变量（首选）
```stata
gen iv_climate_lag2 = L2.climateRiskPublicView
```
- **优点**：不存在同期共线性问题
- **理论依据**：过去的气候风险表达影响当前表达，但不直接影响当前碳排放
- **识别条件**：满足相关性和外生性要求

### 2. 省份层面工具变量（次选）
```stata
bysort Prvcnm_id year: egen iv_prov_mean_all = mean(climateRiskPublicView)
```
- **优点**：地理聚合，避免"排除自身"的共线性问题
- **理论依据**：省份层面的气候风险认知具有空间相关性

### 3. 行业层面工具变量（备选）
```stata
bysort industryCode year: egen iv_ind_mean_all = mean(climateRiskPublicView)
```
- **优点**：行业聚合，反映行业内的共同趋势
- **理论依据**：同行业企业面临相似的气候风险关注

## 🚀 运行方法

直接运行：
```stata
do climate_risk_iv_analysis.do
```

代码会自动：
1. 检查各工具变量的有效性
2. 优先使用滞后工具变量
3. 备选使用省份和行业层面工具变量
4. 如果有多个有效工具变量，进行组合回归
5. 生成完整的结果表格

## 📊 预期输出

### 主要结果文件
- `main_results.rtf` - 主要回归结果（Word格式）
- `heterogeneity_analysis.rtf` - 异质性分析
- `robustness_check.rtf` - 稳健性检验
- `descriptive_stats.rtf` - 描述性统计

### 诊断文件
- `iv_diagnostic_report.txt` - 工具变量诊断报告
- `research_summary.txt` - 研究总结

## ✅ 工具变量有效性标准

### 1. 相关性检验
- **第一阶段F统计量 > 10**（经验法则）
- **第一阶段F统计量 > 16.38**（Stock-Yogo临界值，更严格）

### 2. 外生性检验
- 工具变量不应直接影响因变量（p > 0.05）
- 过度识别检验：Hansen J统计量 p > 0.05

### 3. 内生性检验
- Durbin-Wu-Hausman检验 p < 0.05（表明需要使用工具变量）

## 🔧 如果仍有问题

1. **检查数据质量**：确保有足够的时间和截面变异
2. **考虑外部工具变量**：政策冲击、自然实验等
3. **重新考虑识别策略**：是否需要调整研究设计

## 📝 主要改进

1. **简化了工具变量构建**：避免过度复杂的"排除自身"计算
2. **优先使用滞后变量**：最不容易出现共线性问题
3. **增加了错误处理**：自动跳过无效的工具变量
4. **提供了多种备选方案**：确保至少有一个工具变量可用

现在代码应该能够成功运行并生成有效的工具变量回归结果。
