/*==============================================================================
* 文件名: data_preprocessing.do
* 功能: 数据预处理，包括缩尾处理和变量清理
* 配合主分析文件: climate_risk_iv_analysis.do
* 日期: 2025年1月
*==============================================================================*/

clear all
set more off

* 设置工作目录
cd "/Users/<USER>/Desktop/clitansz"

* 加载原始数据
use "alldata9.dta", clear

/*==============================================================================
* 第一部分：连续变量缩尾处理（Winsorize）
*==============================================================================*/

* 安装winsor2命令（如果尚未安装）
* ssc install winsor2

* 对主要连续变量进行1%和99%分位数缩尾处理
* 核心变量
winsor2 climateRiskPublicView, replace cuts(1 99)
winsor2 carbonIntensityRegional, replace cuts(1 99)

* 企业财务变量
winsor2 firmSize, replace cuts(1 99)
winsor2 firmAge, replace cuts(1 99) 
winsor2 leverage, replace cuts(1 99)
winsor2 ownershipBalance, replace cuts(1 99)
winsor2 tobinsQ1, replace cuts(1 99)
winsor2 returnOnEquity, replace cuts(1 99)
winsor2 returnOnAssets, replace cuts(1 99)

* 研发相关变量
winsor2 rdIntensity, replace cuts(1 99)
winsor2 rdExpenditure, replace cuts(1 99)
winsor2 rdStaff, replace cuts(1 99)
winsor2 rdStaffRatio, replace cuts(1 99)

* 创新质量变量
winsor2 citationExSelfCumulative, replace cuts(1 99)
winsor2 citationTotalCumulative, replace cuts(1 99)

* 企业社会责任
winsor2 csrScore, replace cuts(1 99)

* 地区经济变量
winsor2 gdpPerCapita, replace cuts(1 99)
winsor2 secondaryIndustryRatio, replace cuts(1 99)
winsor2 gdpRegional, replace cuts(1 99)

* 现金流变量
winsor2 cashFlowOps1, replace cuts(1 99)
winsor2 cashFlowOps2, replace cuts(1 99)

* 融资约束指标
winsor2 fcIndex, replace cuts(1 99)
winsor2 wwIndex, replace cuts(1 99)
winsor2 kzIndex, replace cuts(1 99)
winsor2 saIndex, replace cuts(1 99)

/*==============================================================================
* 第二部分：缺失值处理和样本筛选
*==============================================================================*/

* 删除关键变量缺失的观测值
drop if missing(stockCode) | missing(year)
drop if missing(climateRiskPublicView) | missing(carbonIntensityRegional)
drop if missing(industryCode) | missing(Prvcnm_id) | missing(CITYCODE)

* 删除ST和*ST公司（如果有相关标识）
* drop if regexm(stockName, "\*ST|ST")

* 删除金融行业（如果需要）
* drop if substr(industryCode, 1, 1) == "J"  // 根据行业代码删除金融业

* 保留主要分析期间的数据
keep if year >= 2011 & year <= 2022

/*==============================================================================
* 第三部分：生成辅助变量
*==============================================================================*/

* 生成企业年龄的对数
gen ln_firmAge = ln(firmAge + 1)
label variable ln_firmAge "企业年龄对数"

* 生成企业规模的对数（如果firmSize不是对数形式）
gen ln_firmSize = firmSize  // 假设firmSize已经是对数形式
label variable ln_firmSize "企业规模对数"

* 生成人均GDP的对数
gen ln_gdpPerCapita = ln(gdpPerCapita)
label variable ln_gdpPerCapita "人均GDP对数"

* 生成研发强度的平方项（用于非线性检验）
gen rdIntensity_sq = rdIntensity^2
label variable rdIntensity_sq "研发强度平方项"

* 生成企业所有制虚拟变量（如果stateOwned不是0-1变量）
replace stateOwned = 1 if stateOwned > 0 & !missing(stateOwned)
replace stateOwned = 0 if stateOwned == 0
label variable stateOwned "国有企业虚拟变量"

* 生成行业虚拟变量（基于industryCode）
encode industryCode, gen(industry_num)
tabulate industry_num, gen(ind_)
label variable industry_num "行业编码"

* 生成省份虚拟变量
tabulate Prvcnm_id, gen(prov_)

* 生成年份虚拟变量
tabulate year, gen(year_)

/*==============================================================================
* 第四部分：数据质量检查
*==============================================================================*/

* 检查主要变量的分布
summarize climateRiskPublicView carbonIntensityRegional firmSize firmAge leverage, detail

* 检查极端值
list stockCode year climateRiskPublicView carbonIntensityRegional if climateRiskPublicView > 10 | climateRiskPublicView < -10
list stockCode year climateRiskPublicView carbonIntensityRegional if carbonIntensityRegional > 100 | carbonIntensityRegional < 0

* 检查面板数据的平衡性
xtset stockCode year
xtdescribe

* 生成样本统计报告
display "数据预处理完成！"
display "最终样本观测值数量: " _N
display "企业数量: " r(N)
display "时间跨度: " r(Tmin) "-" r(Tmax)

/*==============================================================================
* 第五部分：保存预处理后的数据
*==============================================================================*/

* 保存清理后的数据
save "alldata9_cleaned.dta", replace

* 生成数据说明文档
file open desc using "data_description.txt", write replace
file write desc "数据预处理说明" _n
file write desc "==================" _n
file write desc "原始数据文件: alldata9.dta" _n
file write desc "处理后数据文件: alldata9_cleaned.dta" _n
file write desc "处理时间: " c(current_date) " " c(current_time) _n
file write desc "样本期间: 2011-2022年" _n
file write desc "最终观测值数量: " _N _n
file write desc "" _n
file write desc "主要处理步骤:" _n
file write desc "1. 连续变量1%-99%缩尾处理" _n
file write desc "2. 删除关键变量缺失的观测值" _n
file write desc "3. 生成辅助变量和虚拟变量" _n
file write desc "4. 数据质量检查" _n
file close desc

display "数据预处理完成！"
display "清理后的数据已保存为: alldata9_cleaned.dta"
display "数据说明文档已保存为: data_description.txt"

/*==============================================================================
* 第六部分：生成描述性统计表
*==============================================================================*/

* 主要变量描述性统计
estpost summarize climateRiskPublicView carbonIntensityRegional ///
    firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity ///
    secondaryIndustryRatio gdpPerCapita csrScore rdIntensity ///
    citationExSelfCumulative greenFinanceReform qhsiyin

esttab using "descriptive_stats.rtf", replace ///
    cells("mean(fmt(3)) sd(fmt(3)) min(fmt(3)) max(fmt(3)) count(fmt(0))") ///
    title("主要变量描述性统计") ///
    nomtitles nonumbers ///
    addnotes("注：所有连续变量均已进行1%-99%缩尾处理")

* 按年份的描述性统计
bysort year: estpost summarize climateRiskPublicView carbonIntensityRegional
esttab using "yearly_stats.rtf", replace ///
    cells("mean(fmt(3)) sd(fmt(3))") ///
    title("主要变量按年份描述性统计") ///
    mtitles("2011" "2012" "2013" "2014" "2015" "2016" "2017" "2018" "2019" "2020" "2021" "2022")

display "描述性统计表已生成："
display "- descriptive_stats.rtf: 主要变量统计"
display "- yearly_stats.rtf: 按年份统计"
