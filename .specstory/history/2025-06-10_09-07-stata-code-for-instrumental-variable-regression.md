<!-- Generated by SpecStory -->

# Stata code for instrumental variable regression (2025-06-10 17:07:42)

_**User**_

@项目背景.md 我现在需要做一个工具变量法
请你给我相应的生成stata代码

ivreghdfe
采用公司年份双向固定效应,缩尾1%，聚类到城市层面

---

_**Assistant**_

根据你的需求，下面是一个基于 Stata 的工具变量法（IV回归）代码示例，使用 `ivreghdfe` 命令，包含公司和年份双向固定效应，1%缩尾，并将标准误聚类到城市层面。假设你的城市变量为 city（如有不同请替换），工具变量为 kuandai，自变量为 clipub，因变量为 tpfqd，控制变量可根据需要调整。

```stata
* 1. 1%缩尾处理（以tpfqd为例，其他变量可类似处理）
foreach var in tpfqd clipub kuandai SIZE AGE1 LEV BALANCE SOE {
    qui su `var', detail
    gen `var'_w = `var'
    replace `var'_w = r(p1) if `var' < r(p1)
    replace `var'_w = r(p99) if `var' > r(p99)
}

* 2. 工具变量回归，双向固定效应，城市聚类
ivreghdfe tpfqd_w (clipub_w = kuandai_w) SIZE_w AGE1_w LEV_w BALANCE_w SOE_w, absorb(stkcd year) cluster(city)

* 说明：
* - tpfqd_w：缩尾后的因变量
* - clipub_w：缩尾后的内生解释变量
* - kuandai_w：缩尾后的工具变量
* - absorb(stkcd year)：公司和年份双向固定效应
* - cluster(city)：城市层面聚类稳健标准误
* - 控制变量可根据需要增减


---

