# 公众气候风险表达对上市公司碳排放强度影响研究

## 项目概述

本项目研究中国的公众气候风险表达对上市公司碳排放强度的影响，使用工具变量方法解决内生性问题。

**研究期间**: 2011-2022年  
**数据来源**: alldata9.dta  
**样本**: 中国A股上市公司面板数据

## 文件结构

### 主要代码文件

1. **master_analysis.do** - 主执行文件
   - 整合所有分析步骤
   - 自动安装必要的Stata命令
   - 生成完整的分析报告

2. **data_preprocessing.do** - 数据预处理
   - 连续变量缩尾处理（1%-99%）
   - 缺失值处理和样本筛选
   - 生成辅助变量和虚拟变量

3. **climate_risk_iv_analysis.do** - 主要分析文件
   - 构建各类工具变量
   - OLS和IV回归分析
   - 稳健性检验和异质性分析

4. **iv_validation.do** - 工具变量验证
   - 第一阶段F统计量检验
   - 工具变量外生性检验
   - 过度识别检验和内生性检验

### 工具变量类型

#### 1. 地理/区域聚合工具变量
- `iv_city_other_mean`: 同城市其他企业气候风险表达均值
- `iv_prov_other_mean`: 同省份其他企业气候风险表达均值
- `iv_city_ind_other_mean`: 同城市同行业其他企业均值
- `iv_prov_ind_other_mean`: 同省份同行业其他企业均值

#### 2. 行业聚合工具变量
- `iv_ind_other_mean`: 同行业其他企业气候风险表达均值
- `iv_ind_other_prov_mean`: 同行业不同省份企业均值

#### 3. 滞后期工具变量
- `iv_climate_lag1`: 公众气候风险表达滞后1期
- `iv_climate_lag2`: 公众气候风险表达滞后2期
- `iv_climate_lag3`: 公众气候风险表达滞后3期
- `iv_hist_mean`: 企业历史气候风险表达均值（2011-2013年）
- `iv_city_hist_mean`: 城市历史气候风险表达均值（2011-2013年）

## 使用方法

### 快速开始

1. 确保数据文件 `alldata9.dta` 在工作目录中
2. 在Stata中运行主执行文件：
   ```stata
   do master_analysis.do
   ```

### 分步执行

如果需要分步执行，按以下顺序运行：

1. 数据预处理：
   ```stata
   do data_preprocessing.do
   ```

2. 主要分析：
   ```stata
   do climate_risk_iv_analysis.do
   ```

3. 工具变量验证：
   ```stata
   do iv_validation.do
   ```

## 主要变量

### 核心变量
- **因变量**: `carbonIntensityRegional` - 碳排放强度
- **自变量**: `climateRiskPublicView` - 公众气候风险表达指数

### 控制变量
- **企业层面**: 企业规模、年龄、杠杆率、股权制衡度、所有制、托宾Q值、ROE
- **地区层面**: 第二产业比重、人均GDP

### 调节变量
- `greenFinanceReform`: 绿色金融改革试点
- `qhsiyin`: 气候适应型城市

## 输出文件

### 主要结果
- `final_results.rtf`: 主要回归结果表格
- `heterogeneity_analysis.rtf`: 异质性分析结果
- `descriptive_stats.rtf`: 描述性统计表
- `yearly_stats.rtf`: 按年份统计表

### 诊断报告
- `iv_diagnostic_report.txt`: 工具变量诊断报告
- `data_description.txt`: 数据处理说明
- `research_summary.txt`: 研究总结报告

### 数据文件
- `alldata9_cleaned.dta`: 清理后的数据
- `alldata9_with_iv.dta`: 包含工具变量的数据

### 图表文件
- `iv_city_scatter.png`: 城市工具变量散点图
- `iv_prov_scatter.png`: 省份工具变量散点图

## 工具变量有效性检验

### 1. 相关性检验（第一阶段F统计量）
- **标准**: F > 10（经验法则）或 F > 16.38（Stock-Yogo临界值）
- **解释**: 工具变量与内生变量应强相关

### 2. 外生性检验
- **方法**: 工具变量与因变量的直接回归
- **标准**: 系数应不显著（p > 0.05）
- **解释**: 工具变量不应直接影响因变量

### 3. 过度识别检验（Hansen J检验）
- **适用**: 多个工具变量情况
- **标准**: p > 0.05
- **解释**: 工具变量满足外生性条件

### 4. 内生性检验（Durbin-Wu-Hausman检验）
- **标准**: p < 0.05表示存在内生性
- **解释**: 需要使用工具变量方法

## 稳健性检验

1. **不同聚类方式**: 城市、省份、行业、双向聚类
2. **不同工具变量组合**: 单个vs多个工具变量
3. **样本分组**: 按所有制、企业规模、政策试点分组

## 异质性分析

1. **企业所有制**: 国有 vs 非国有企业
2. **政策环境**: 绿色金融试点 vs 非试点地区
3. **城市特征**: 气候适应型 vs 非气候适应型城市
4. **企业规模**: 大企业 vs 小企业

## 注意事项

1. **数据要求**: 确保关键变量无大量缺失值
2. **工具变量选择**: 优先使用通过所有有效性检验的工具变量
3. **结果解释**: 重点关注IV估计结果，OLS结果仅作对比
4. **稳健性**: 建议使用多种工具变量验证结果稳健性

## 技术要求

- **Stata版本**: 建议使用Stata 15或更高版本
- **必要命令**: reghdfe, ivreghdfe, winsor2, estout（代码会自动安装）
- **内存要求**: 建议设置足够的matsize（代码中设为10000）

## 联系信息

如有问题，请查看：
1. `research_summary.txt` - 研究总结
2. `iv_diagnostic_report.txt` - 工具变量诊断
3. Stata输出的详细统计结果

---

**最后更新**: 2025年1月  
**版本**: 1.0
