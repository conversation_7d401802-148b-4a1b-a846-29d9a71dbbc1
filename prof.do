clear all
set more off

use "/Users/<USER>/Desktop/clitansz/alldata8.dta"

* 设置面板数据
xtset stkcd year

* 对连续变量进行1%的缩尾处理
winsor2 tpfqd clipub sumshzr RDPerson RDPersonRatio RDSpendSum RDSpendSumRatio RDExpenses RDInvest RDInvestRatio RDInvestNetprofitRatio SIZE AGE1 LEV BALANCE TobinQ1 TobinQ2 TobinQ3 TobinQ4 ROE TOP1 第二产业增加值占GDP比重 人均地区生产总值元, cuts(1 99) replace

************************ 主回归 ************************

* 基准回归：公众气候风险表达对碳排放强度的影响
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)

* 工具变量回归
ivreghdfe tpfqd (clipub = kuandai) SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE) first

* 中介效应1：企业社会责任
reghdfe sumshzr clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)

* 中介效应2：研发投入（分别使用不同指标）
* 创建临时存储结果的空矩阵
eststo clear

* 研发投入系列回归
eststo: reghdfe RDPerson clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
eststo: reghdfe RDPersonRatio clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
eststo: reghdfe RDSpendSum clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
eststo: reghdfe RDSpendSumRatio clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
eststo: reghdfe RDExpenses clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
eststo: reghdfe RDInvest clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
eststo: reghdfe RDInvestRatio clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
eststo: reghdfe RDInvestNetprofitRatio clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)

* 输出研发投入汇总表格
esttab using "研发投入中介效应.rtf", ///
    title("研发投入中介效应分析") ///
    mtitle("人员数量" "人员占比" "支出总额" "支出占比" "研发费用" "研发投入" "投入占比" "净利润比") ///
    star(* 0.1 ** 0.05 *** 0.01) ///
    b(%9.4f) t(%9.2f) ar2 ///
    scalars("N 观测值" "r2_a Adj-R2" "F F值") ///
    compress replace

* 调节效应1：绿色金融改革创新试验区
reghdfe tpfqd c.clipub##c.greenfin SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)

* 调节效应2：气候适应性城市
reghdfe tpfqd c.clipub##c.qhsiyin SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)

************************ 稳健性检验 ************************

* 1. PSM倾向得分匹配
* 生成处理组（以clipub中位数为界）
sum clipub, detail
gen treat = (clipub >= r(p50))
* PSM匹配
psmatch2 treat SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, outcome(tpfqd) neighbor(1) common
* 使用匹配后样本进行回归
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1 if _support==1, absorb(stkcd year) cluster(CITYCODE)

* 2. 更换控制变量度量方式
* 使用不同的Tobin Q
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ2 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ3 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ4 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)

* 3. 子样本检验
* 剔除新冠疫情期间（2020年后）
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1 if year < 2020, absorb(stkcd year) cluster(CITYCODE)

* 按企业规模分组（以总资产中位数为界）
sum SIZE, detail
gen size_group = (SIZE >= r(p50))
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1 if size_group==0, absorb(stkcd year) cluster(CITYCODE)
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1 if size_group==1, absorb(stkcd year) cluster(CITYCODE)

* 4. 不同固定效应组合
* 仅控制公司固定效应
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd) cluster(CITYCODE)

* 仅控制年份固定效应
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(year) cluster(CITYCODE)

* 控制公司、年份和省份-年份固定效应
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year PROVCD#year) cluster(CITYCODE)

* 5. 动态面板GMM估计
xtabond2 tpfqd L.tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1 i.year, gmm(L.tpfqd clipub, lag(2 .)) iv(SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1 i.year) twostep robust

* 6. 改进的安慰剂检验
* 6.1 时间维度打乱
preserve
set seed 123
gen random_year = runiform()
sort random_year
gen fake_year = year[_n]
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1 if fake_year != year, absorb(stkcd fake_year) cluster(CITYCODE)
restore

* 6.2 空间维度打乱
preserve
set seed 456
gen random_city = runiform()
sort random_city
gen fake_CITYCODE = CITYCODE[_n]
reghdfe tpfqd clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1 if fake_CITYCODE != CITYCODE, absorb(stkcd year) cluster(fake_CITYCODE)
restore

* 6.3 多次随机检验（Monte Carlo模拟）
* 创建存储结果的变量
gen sig_count = 0
forvalues i = 1/1000 {
    preserve
    qui {
        set seed `i'
        gen random_clipub = rnormal(0,1)  // 使用正态分布而不是均匀分布
        reghdfe tpfqd random_clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
        test random_clipub = 0
        if r(p) < 0.05 {
            replace sig_count = sig_count + 1 in 1
        }
    }
    restore
}
di "Significant results proportion: " sig_count[1]/1000

* 6.4 行业层面的安慰剂检验
preserve
set seed 789
bysort INDCD: gen random_ind_clipub = clipub[_n]
reghdfe tpfqd random_ind_clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1 if random_ind_clipub != clipub, absorb(stkcd year) cluster(CITYCODE)
restore

* 6.5 提前期检验（预期效应检验）
gen lead1_clipub = f.clipub
gen lead2_clipub = f2.clipub
reghdfe tpfqd lead1_clipub lead2_clipub SOE SIZE AGE1 LEV 第二产业增加值占GDP比重 人均地区生产总值元 BALANCE TobinQ1 ROE TOP1, absorb(stkcd year) cluster(CITYCODE)
