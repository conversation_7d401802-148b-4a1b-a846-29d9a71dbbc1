// 测试宏定义
clear all

// 定义控制变量宏
global firm_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity"
global region_controls "secondaryIndustryRatio gdpPerCapita"
global all_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity secondaryIndustryRatio gdpPerCapita"

// 显示宏内容
display "firm_controls: $firm_controls"
display "region_controls: $region_controls"
display "all_controls: $all_controls"

// 载入数据进行测试
use "alldata9.dta", clear

// 测试样本标记
gen sample_main = 1 if !missing(carbonIntensityRegional, climateRiskPublicView, $all_controls)

// 检查样本标记是否成功
count if sample_main == 1
display "样本数量: " r(N)

// 测试描述性统计
summarize carbonIntensityRegional climateRiskPublicView $all_controls if sample_main==1
