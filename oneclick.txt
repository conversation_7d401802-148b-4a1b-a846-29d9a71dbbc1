---

help for oneclick
-----------------

Title

    oneclick —— Helps you to quickly screen for control variables that keep
                the explanatory variables at a certain level of significance.

Syntax

    oneclick y controls, method(regression) pvalue(p-value) fixvar(x and
             other FE) [ options(extra-options) zvalue threshold(int)
             saveplace(filename) best full ]

    If you're using the reghdfe command, put the fixed effects into the
    absorb as much as possible to speed up the calculations.

    If you're not used to the new version of oneclick, I've kept oneclick5 in
    this version. you can use oneclick5 to implement the functionality of the
    previous version.

Description

    By entering your control variables, the oneclick command helps you to
    select all true subsets of the control variables and add them to the
    regression in turn, and at the end only the combinations at the level of
    significance you are satisfied with are listed. I constructed the subset
    filtering method from scratch based on the bitmap algorithm, which is
    faster than tuples.

Requirements

    varlist specifies the dependent variable and control variables to be
        screened.  If you type oneclick a b c, then a is your dependent
        variable, b and c are the control variables waiting to be filtered.

    method(regression) specifies the estimator you want to use.

    pvalue(p-value) specifies the level of significance.

    fixvar(varlist) specifies the independent variable and other variables
        that you want to fix in regression. If you type a b c, then a is you
        independent variable, b and c are the integral control variables.

    options(extra-options) specifies the additional options in regression.
        If you use reghdfe, you can add o(absorb(#)).

    zvalue specifies whether regression is judged by z-values.If you use a
        regression like logit, probit, or xtreg (with default), you must add
        the z option

    threshold specifies the minimum number of optional control variables you
        need to keep in the result.

    saveplace specifies what you need to rename the result file to.

    best let oneclick automatically pick the best regression results for you.

    full let oneclick fill in the results for you to make it more readable.

Results

    After running oneclick, you will see a dta file named subset in the
    current working directory. Among them, the variable subset represents the
    control variable that can make the explanatory variable significant, the
    variable positive takes 1 to indicate positive significance, and takes 0
    to indicate negative significance.

Code examples

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in regress.

    - sysuse auto.dta, clear
    - oneclick price mpg rep78, fix(weight) p(0.1) m(reg)

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in regress with fixed-effect.
    **- Individual fixed-effect: foreign.

    - sysuse auto.dta, clear
    - oneclick price mpg rep78, fix(weight i.foreign) p(0.1) m(reg)

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in regress with fixed-effect and robust
    standard error.
    **- Individual fixed-effect: foreign.

    - sysuse auto.dta, clear
    - oneclick price mpg rep78, fix(weight i.foreign) p(0.1) m(reg)
    o(vce(robust))

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in regress with fixed-effect and cluster
    standard error.
    **- Individual fixed-effect: foreign.

    - sysuse auto.dta, clear
    - oneclick price mpg rep78, fix(weight i.foreign) p(0.1) m(reg)
    o(vce(cluster foreign))

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in xtreg with fixed-effect.
    **- Individual fixed-effect: foreign.
    **- Time fixed-effect: year.

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - xtset foreign year
    - oneclick price mpg rep78, fix(weight i.year) p(0.1) m(xtreg) o(fe)

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in xtreg with fixed-effect and robust
    standard error.
    **- Individual fixed-effect: foreign.
    **- Time fixed-effect: year.

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - xtset foreign year
    - oneclick price mpg rep78, fix(weight i.year) p(0.1) m(xtreg) o(fe
    vce(robust))

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in xtreg with fixed-effect and cluster
    standard error.
    **- Individual fixed-effect: foreign.
    **- Time fixed-effect: year.

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - xtset foreign year
    - oneclick price mpg rep78, fix(weight i.year) p(0.1) m(xtreg) o(fe
    vce(cluster foreign))

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in reghdfe with fixed-effect.
    **- Individual fixed-effect: foreign.
    **- Time fixed-effect: year.

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - oneclick price mpg rep78, fix(weight) p(0.1) m(reghdfe)
    o(absorb(foreign year))

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in reghdfe with fixed-effect and robust
    standard error.
    **- Individual fixed-effect: foreign.
    **- Time fixed-effect: year.

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - oneclick price mpg rep78, fix(weight) p(0.1) m(reghdfe)
    o(absorb(foreign year) vce(robust))

    *- Selecting the combination from mpg and rep78 that will make weight
    significant at the 10% level in reghdfe with fixed-effect and cluster
    standard error.
    **- Individual fixed-effect: foreign.
    **- Time fixed-effect: year.

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - oneclick price mpg rep78, fix(weight) p(0.1) m(reghdfe)
    o(absorb(foreign year) vce(cluster foreign))

    *- If you use regression methods such as logit or probit that base
    significance judgments on z-values rather than t-values, don't forget to
    add the z option to the end of oneclick.

    - sysuse auto.dta, clear
    - oneclick foreign mpg rep78, fix(weight) p(0.1) m(logit) z
    - oneclick foreign mpg rep78, fix(weight) p(0.1) m(probit) z

Video tutorial

    Watch the latest video at this link: oneclick video tutorial

Author

    Basic information
    Name: Shutter Zor (左祥太)
    Affiliation: Accounting Department, Xiamen University.
    E-mail: <EMAIL>

    Other information
    Blog: blog link
    Bilibili: 拿铁一定要加冰
    WeChat Official Account: OneStata

Other commands i have written

    onetext (if installed)           ssc install onetext (to install)
    econsig (if installed)           ssc install econsig (to install)
    wordcloud (if installed)         ssc install wordcloud (to install)

Acknowledgments

    Thank you to Professor Yujun,Lian (arlionn) for his programming syntax
    guidance and Professor Christopher F. Baum for his careful bug checking.
    Thanks to Bilibili users (导导们) for their suggestions on the initial
    construction of this project.
