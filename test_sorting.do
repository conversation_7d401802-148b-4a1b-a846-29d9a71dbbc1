/*==============================================================================
* 测试文件：解决数据排序问题
* 功能：检查和修复面板数据排序问题
*==============================================================================*/

clear all
set more off

* 设置工作目录
cd "/Users/<USER>/Desktop/clitansz"

* 加载数据
use "alldata9.dta", clear

* 检查数据结构
describe stockCode year
summarize stockCode year

* 检查是否有缺失的关键变量
count if missing(stockCode)
display "stockCode缺失观测数: " r(N)

count if missing(year)
display "year缺失观测数: " r(N)

* 删除关键变量缺失的观测
drop if missing(stockCode) | missing(year)

* 检查stockCode和year的数据类型
display "stockCode的数据类型："
describe stockCode
display "year的数据类型："
describe year

* 确保stockCode和year是数值型
* 如果stockCode是字符串，需要转换
capture confirm numeric variable stockCode
if _rc != 0 {
    display "stockCode是字符串变量，正在转换为数值型..."
    encode stockCode, gen(stockCode_num)
    drop stockCode
    rename stockCode_num stockCode
}

* 确保year是数值型
capture confirm numeric variable year
if _rc != 0 {
    display "year是字符串变量，正在转换为数值型..."
    destring year, replace
}

* 检查数据范围
summarize stockCode year, detail

* 检查是否有重复观测
duplicates report stockCode year
local dup_count = r(unique_value)
if `dup_count' < r(N) {
    display "发现重复观测，正在删除..."
    duplicates drop stockCode year, force
}

* 强制排序
display "正在排序数据..."
sort stockCode year

* 设置面板数据
display "设置面板数据结构..."
xtset stockCode year

* 检查面板数据设置是否成功
xtdescribe

* 测试生成滞后变量
display "测试生成滞后变量..."
capture gen test_lag = L.climateRiskPublicView
if _rc == 0 {
    display "✓ 滞后变量生成成功！"
    drop test_lag
}
else {
    display "✗ 滞后变量生成失败，错误代码: " _rc
    
    * 进一步诊断
    display "进一步诊断问题..."
    
    * 检查是否有非连续的年份
    bysort stockCode: gen year_gap = year - year[_n-1] if _n > 1
    summarize year_gap
    count if year_gap != 1 & !missing(year_gap)
    display "非连续年份观测数: " r(N)
    
    * 检查面板是否平衡
    xtdescribe
    display "面板数据描述已显示"
    
    drop year_gap
}

* 保存排序后的数据
save "alldata9_sorted.dta", replace

display "数据排序测试完成！"
display "如果滞后变量生成成功，可以继续运行主分析文件"
display "排序后的数据已保存为: alldata9_sorted.dta"
