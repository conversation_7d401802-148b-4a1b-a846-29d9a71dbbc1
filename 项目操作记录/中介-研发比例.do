********************************************************************************
* 项目：公众气候风险表达通过研发投入影响碳排放强度的中介效应分析
* 作者：Claude Assistant
* 日期：2024年
********************************************************************************

* 设置工作目录（请根据实际情况修改）
cd "/Users/<USER>/Desktop/clipubtan"

* 设置输出格式
set linesize 255
set more off

********************************************************************************
* 第一部分：数据准备
********************************************************************************

* 使用主数据集
use "all.dta", clear

* 对连续变量进行1%水平的缩尾处理
winsor2 CemissionIntensity clipub RDSpendSumRatio SIZE LEV GROWTH ROA MHOLD TOP1, cuts(1 99) replace

********************************************************************************
* 第二部分：基础中介效应分析
********************************************************************************

* 总效应分析（c路径）：clipub -> CemissionIntensity
eststo clear
eststo: reghdfe CemissionIntensity clipub SIZE LEV, absorb(stkcd year) vce(cluster city)
outreg2 using "回归分析结果_中介效应.doc", replace ctitle(总效应) addtext(公司固定效应, YES, 年份固定效应, YES) ///
        addstat(Within R-squared, e(r2_within)) dec(6)

* 中介效应第一步（a路径）：clipub -> RDSpendSumRatio
eststo: reghdfe RDSpendSumRatio clipub SIZE LEV, absorb(stkcd year) vce(cluster city)
outreg2 using "回归分析结果_中介效应.doc", append ctitle(中介效应a路径) addtext(公司固定效应, YES, 年份固定效应, YES) ///
        addstat(Within R-squared, e(r2_within)) dec(6)

* 中介效应第二步（b路径和c'路径）：clipub + RDSpendSumRatio -> CemissionIntensity
eststo: reghdfe CemissionIntensity clipub RDSpendSumRatio SIZE LEV, absorb(stkcd year) vce(cluster city)
outreg2 using "回归分析结果_中介效应.doc", append ctitle(中介效应b路径) addtext(公司固定效应, YES, 年份固定效应, YES) ///
        addstat(Within R-squared, e(r2_within)) dec(6)

********************************************************************************
* 第三部分：稳健性检验
********************************************************************************

* 模型1：基本控制变量
eststo clear
eststo: reghdfe CemissionIntensity clipub SIZE LEV, absorb(stkcd year) vce(cluster city)
eststo: reghdfe RDSpendSumRatio clipub SIZE LEV, absorb(stkcd year) vce(cluster city)
eststo: reghdfe CemissionIntensity clipub RDSpendSumRatio SIZE LEV, absorb(stkcd year) vce(cluster city)
esttab using "回归分析结果_稳健性.doc", replace ///
       title("稳健性检验：基本控制变量模型") ///
       mtitles("总效应" "中介效应a路径" "中介效应b路径") ///
       star(* 0.1 ** 0.05 *** 0.01) b(6) t(2)

* 模型2：加入更多公司特征
eststo clear
eststo: reghdfe CemissionIntensity clipub SIZE LEV GROWTH ROA, absorb(stkcd year) vce(cluster city)
eststo: reghdfe RDSpendSumRatio clipub SIZE LEV GROWTH ROA, absorb(stkcd year) vce(cluster city)
eststo: reghdfe CemissionIntensity clipub RDSpendSumRatio SIZE LEV GROWTH ROA, absorb(stkcd year) vce(cluster city)
esttab using "回归分析结果_稳健性.doc", append ///
       title("稳健性检验：加入更多公司特征") ///
       mtitles("总效应" "中介效应a路径" "中介效应b路径") ///
       star(* 0.1 ** 0.05 *** 0.01) b(6) t(2)

* 模型3：加入公司治理变量
eststo clear
eststo: reghdfe CemissionIntensity clipub SIZE LEV GROWTH ROA MHOLD TOP1 BOARD INDBOARD, absorb(stkcd year) vce(cluster city)
eststo: reghdfe RDSpendSumRatio clipub SIZE LEV GROWTH ROA MHOLD TOP1 BOARD INDBOARD, absorb(stkcd year) vce(cluster city)
eststo: reghdfe CemissionIntensity clipub RDSpendSumRatio SIZE LEV GROWTH ROA MHOLD TOP1 BOARD INDBOARD, absorb(stkcd year) vce(cluster city)
esttab using "回归分析结果_稳健性.doc", append ///
       title("稳健性检验：加入公司治理变量") ///
       mtitles("总效应" "中介效应a路径" "中介效应b路径") ///
       star(* 0.1 ** 0.05 *** 0.01) b(6) t(2)

********************************************************************************
* 第四部分：滞后效应检验
********************************************************************************

* 生成滞后变量
sort stkcd year
by stkcd: gen clipub_lag1 = clipub[_n-1]

* 滞后一期的中介效应分析
eststo clear
eststo: reghdfe CemissionIntensity clipub_lag1 SIZE LEV, absorb(stkcd year) vce(cluster city)
eststo: reghdfe RDSpendSumRatio clipub_lag1 SIZE LEV, absorb(stkcd year) vce(cluster city)
eststo: reghdfe CemissionIntensity clipub_lag1 RDSpendSumRatio SIZE LEV, absorb(stkcd year) vce(cluster city)
esttab using "回归分析结果_滞后效应.doc", replace ///
       title("滞后效应检验") ///
       mtitles("总效应" "中介效应a路径" "中介效应b路径") ///
       star(* 0.1 ** 0.05 *** 0.01) b(6) t(2)

********************************************************************************
* 第五部分：分组检验
********************************************************************************

* 按企业规模分组
egen size_median = median(SIZE)
gen size_group = (SIZE >= size_median)
label define size_label 0 "小规模企业" 1 "大规模企业"
label values size_group size_label

* 大规模企业组
eststo clear
eststo: reghdfe CemissionIntensity clipub SIZE LEV if size_group==1, absorb(stkcd year) vce(cluster city)
eststo: reghdfe RDSpendSumRatio clipub SIZE LEV if size_group==1, absorb(stkcd year) vce(cluster city)
eststo: reghdfe CemissionIntensity clipub RDSpendSumRatio SIZE LEV if size_group==1, absorb(stkcd year) vce(cluster city)
esttab using "回归分析结果_分组.doc", replace ///
       title("大规模企业组") ///
       mtitles("总效应" "中介效应a路径" "中介效应b路径") ///
       star(* 0.1 ** 0.05 *** 0.01) b(6) t(2)

* 小规模企业组
eststo clear
eststo: reghdfe CemissionIntensity clipub SIZE LEV if size_group==0, absorb(stkcd year) vce(cluster city)
eststo: reghdfe RDSpendSumRatio clipub SIZE LEV if size_group==0, absorb(stkcd year) vce(cluster city)
eststo: reghdfe CemissionIntensity clipub RDSpendSumRatio SIZE LEV if size_group==0, absorb(stkcd year) vce(cluster city)
esttab using "回归分析结果_分组.doc", append ///
       title("小规模企业组") ///
       mtitles("总效应" "中介效应a路径" "中介效应b路径") ///
       star(* 0.1 ** 0.05 *** 0.01) b(6) t(2)

* 计算中介效应大小
* 使用整体样本的系数计算
matrix list e(b)
display "中介效应大小 = " _b[RDSpendSumRatio] * _b[clipub]
display "总效应 = " _b[clipub]
display "中介效应占比 = " (_b[RDSpendSumRatio] * _b[clipub]) / _b[clipub] * 100
