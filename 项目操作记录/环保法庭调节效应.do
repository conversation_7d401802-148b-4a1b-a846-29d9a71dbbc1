* ========================
* 环保法庭调节效应回归分析主代码
* 本代码包含所有变量缩尾、变量生成、回归命令，便于手动复刻
* ========================

* 1. 读取数据
use "/Users/<USER>/Desktop/clipubtan/allivhbft.dta", clear

* 2. 1%缩尾处理（所有用到的变量都要缩尾）
* --- 处理hbft
su hbft, detail
local p1 = r(p1)
local p99 = r(p99)
gen hbft_c = hbft
replace hbft_c = `p1' if hbft_c < `p1'
replace hbft_c = `p99' if hbft_c > `p99'
* --- 处理clipub
su clipub, detail
local p1 = r(p1)
local p99 = r(p99)
gen clipub_c = clipub
replace clipub_c = `p1' if clipub_c < `p1'
replace clipub_c = `p99' if clipub_c > `p99'
* --- 处理hbft_clipub
su hbft_clipub, detail
local p1 = r(p1)
local p99 = r(p99)
gen hbft_clipub_c = hbft_clipub
replace hbft_clipub_c = `p1' if hbft_clipub_c < `p1'
replace hbft_clipub_c = `p99' if hbft_clipub_c > `p99'
* --- 处理因变量CemissionIntensity
su CemissionIntensity, detail
local p1 = r(p1)
local p99 = r(p99)
gen CemissionIntensity_c = CemissionIntensity
replace CemissionIntensity_c = `p1' if CemissionIntensity_c < `p1'
replace CemissionIntensity_c = `p99' if CemissionIntensity_c > `p99'

* 3. 控制变量缩尾与生成（按需添加）
* --- 常用控制变量
su AGE1, detail
local p1 = r(p1)
local p99 = r(p99)
gen AGE1_c = AGE1
replace AGE1_c = `p1' if AGE1_c < `p1'
replace AGE1_c = `p99' if AGE1_c > `p99'
su SIZE, detail
local p1 = r(p1)
local p99 = r(p99)
gen SIZE_c = SIZE
replace SIZE_c = `p1' if SIZE_c < `p1'
replace SIZE_c = `p99' if SIZE_c > `p99'
su LEV, detail
local p1 = r(p1)
local p99 = r(p99)
gen LEV_c = LEV
replace LEV_c = `p1' if LEV_c < `p1'
replace LEV_c = `p99' if LEV_c > `p99'
su ROA, detail
local p1 = r(p1)
local p99 = r(p99)
gen ROA_c = ROA
replace ROA_c = `p1' if ROA_c < `p1'
replace ROA_c = `p99' if ROA_c > `p99'
su SOE, detail
local p1 = r(p1)
local p99 = r(p99)
gen SOE_c = SOE
replace SOE_c = `p1' if SOE_c < `p1'
replace SOE_c = `p99' if SOE_c > `p99'
* --- 公司治理相关
su BOARD, detail
local p1 = r(p1)
local p99 = r(p99)
gen BOARD_c = BOARD
replace BOARD_c = `p1' if BOARD_c < `p1'
replace BOARD_c = `p99' if BOARD_c > `p99'
su INDBOARD, detail
local p1 = r(p1)
local p99 = r(p99)
gen INDBOARD_c = INDBOARD
replace INDBOARD_c = `p1' if INDBOARD_c < `p1'
replace INDBOARD_c = `p99' if INDBOARD_c > `p99'
su DUAL, detail
local p1 = r(p1)
local p99 = r(p99)
gen DUAL_c = DUAL
replace DUAL_c = `p1' if DUAL_c < `p1'
replace DUAL_c = `p99' if DUAL_c > `p99'
su TOP1, detail
local p1 = r(p1)
local p99 = r(p99)
gen TOP1_c = TOP1
replace TOP1_c = `p1' if TOP1_c < `p1'
replace TOP1_c = `p99' if TOP1_c > `p99'
su BALANCE, detail
local p1 = r(p1)
local p99 = r(p99)
gen BALANCE_c = BALANCE
replace BALANCE_c = `p1' if BALANCE_c < `p1'
replace BALANCE_c = `p99' if BALANCE_c > `p99'
* --- 其他常用控制变量
su GROWTH, detail
local p1 = r(p1)
local p99 = r(p99)
gen GROWTH_c = GROWTH
replace GROWTH_c = `p1' if GROWTH_c < `p1'
replace GROWTH_c = `p99' if GROWTH_c > `p99'
su INSTITUTION1, detail
local p1 = r(p1)
local p99 = r(p99)
gen INSTITUTION1_c = INSTITUTION1
replace INSTITUTION1_c = `p1' if INSTITUTION1_c < `p1'
replace INSTITUTION1_c = `p99' if INSTITUTION1_c > `p99'
su CFO1, detail
local p1 = r(p1)
local p99 = r(p99)
gen CFO1_c = CFO1
replace CFO1_c = `p1' if CFO1_c < `p1'
replace CFO1_c = `p99' if CFO1_c > `p99'
su TobinQ1, detail
local p1 = r(p1)
local p99 = r(p99)
gen TobinQ1_c = TobinQ1
replace TobinQ1_c = `p1' if TobinQ1_c < `p1'
replace TobinQ1_c = `p99' if TobinQ1_c > `p99'
su BM1, detail
local p1 = r(p1)
local p99 = r(p99)
gen BM1_c = BM1
replace BM1_c = `p1' if BM1_c < `p1'
replace BM1_c = `p99' if BM1_c > `p99'
su TAT, detail
local p1 = r(p1)
local p99 = r(p99)
gen TAT_c = TAT
replace TAT_c = `p1' if TAT_c < `p1'
replace TAT_c = `p99' if TAT_c > `p99'

* 4. 检查reghdfe是否安装
cap which reghdfe
if _rc ssc install reghdfe, replace

* 5. 回归分析（逐步递进，按需选择控制变量）
* --- 基础模型（无控制变量）
reghdfe CemissionIntensity_c hbft_c clipub_c hbft_clipub_c, absorb(stkcd year) vce(cluster CITYCODE)

* --- 加入AGE1、SIZE、LEV、ROA、SOE
reghdfe CemissionIntensity_c hbft_c clipub_c hbft_clipub_c AGE1_c SIZE_c LEV_c ROA_c SOE_c, absorb(stkcd year) vce(cluster CITYCODE)

* --- 加入公司治理变量
reghdfe CemissionIntensity_c hbft_c clipub_c hbft_clipub_c AGE1_c SIZE_c LEV_c ROA_c SOE_c BOARD_c INDBOARD_c DUAL_c TOP1_c BALANCE_c, absorb(stkcd year) vce(cluster CITYCODE)

* --- 加入成长性、机构持股、现金流、TobinQ等
reghdfe CemissionIntensity_c hbft_c clipub_c hbft_clipub_c AGE1_c SIZE_c LEV_c ROA_c SOE_c BOARD_c INDBOARD_c DUAL_c TOP1_c BALANCE_c GROWTH_c INSTITUTION1_c CFO1_c TobinQ1_c, absorb(stkcd year) vce(cluster CITYCODE)

* --- 加入BM1、TAT等更多变量
reghdfe CemissionIntensity_c hbft_c clipub_c hbft_clipub_c AGE1_c SIZE_c LEV_c ROA_c SOE_c BOARD_c INDBOARD_c DUAL_c TOP1_c BALANCE_c GROWTH_c INSTITUTION1_c CFO1_c TobinQ1_c BM1_c TAT_c, absorb(stkcd year) vce(cluster CITYCODE)

* ========================
* 说明：
* 1. 每一步回归都可单独运行，逐步递进。
* 2. 所有变量均已1%缩尾，避免极端值影响。
* 3. 吸收公司和年份双向固定效应，标准误聚类在城市层面。
* 4. 可根据需要增减控制变量。
* 5. 若变量缺失较多，建议先检查数据完整性。
* ========================
