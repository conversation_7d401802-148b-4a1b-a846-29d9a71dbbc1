cd "/Users/<USER>/Desktop/clitansz/原始dta数据集"
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/碳排放强度.dta"
duplicates report stkcd year
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/控制变量.dta"
duplicates report stkcd year
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/碳排放强度.dta"
destring stkcd, replace
save "/Users/<USER>/Desktop/clitansz/原始dta数据集/碳排放强度.dta", replace
merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/控制变量.dta"
keep if _merge == 3
drop _merge
save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳强度控制.dta"
duplicates report stkcd year
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/绿色专利被引.dta"
duplicates report stkcd year
merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/合并中数据集/碳强度控制.dta"
drop if _merge == 1
drop _merge
save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制专利.dta"
duplicates report stkcd year
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/融资约束.dta"
duplicates report stkcd year
merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制专利.dta"
keep if _merge == 3
drop _merge
save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳强度控制融资.dta"
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta"
use "/Users/<USER>/Desktop/clitansz/数据构建参考文献/社会责任/CNDD-0071 上市公司社会责任研究数据/CNDD-0071 上市公司社会责任报告评价指标.dta"
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta"
rename symbol stkcd
gen year = substr(enddate, 1, 4)
destring year, replace
egen sumshzr = rowtotal(worksafety systemconstruction staffprotection shareholdersprotection ///
                       publicrelations gri environmentprotection deliveryprotection ///
                       deficiency customerprotection creditorprotection certification)save "/Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta", replace
duplicates report stkcd year
duplicates drop stkcd year, force
save "/Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta", replace
merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/合并中数据集/碳强度控制融资.dta"
drop if _merge==1
drop _merge
save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta"
duplicates report stkcd year
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/研发投入.dta"
duplicates report stkcd year









use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta"
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/上市公司所处城市.dta"
duplicates report stkcd year
use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta"
duplicates report stkcd year
sort stkcd year
merge m:1 stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/上市公司所处城市.dta"
keep if _merge==3
drop if _merge
save "/Users/<USER>/Desktop/clipubtan/碳控制融资专利责任城市.dta"
save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta"
use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta"
use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta"
duplicates report stkcd year
sort stkcd year
merge m:1 stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/上市公司所处城市.dta"
keep if _merge==3
drop _merge
save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta", replace
duplicates report CITYCODE year
use "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta"
duplicates report CITYCODE year
rename 地区 CITY
duplicates report CITYCODE CITY year
save "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta", replace
use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta"
duplicates report CITYCODE CITY year
destring CITYCODE, replace
replace CITYCODE = "420600" if inlist(CITY, "襄阳市")
replace CITYCODE = "540400" if inlist(CITY, "林芝市")
replace CITYCODE = "320685" if inlist(CITY, "海安市")
replace CITYCODE = "430182" if inlist(CITY, "宁乡市")
replace CITYCODE = "630200" if inlist(CITY, "海东市")
replace CITYCODE = "331083" if inlist(CITY, "玉环市")
drop if CITYCODE == "None"
destring CITYCODE, replace
save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta", replace
use "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta"
drop if _merge == 2
drop _merge
save "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta", replace
use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta"
merge m:1 CITYCODE CITY year using "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta"
keep if _merge==3
drop _merge
save "/Users/<USER>/Desktop/clitansz/alldata.dta"


use "/Users/<USER>/Desktop/clitansz/alldata.dta", clear
sort stkcd year
keep if year > 2010
gen is_st = strpos(STKNM, "ST") > 0
bysort stkcd: egen any_st = max(is_st)
list if any_st == 1
keep if any_st == 0
drop is_st any_st
save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace
gen is_bank = strpos(STKNM, "银行") > 0
list stkcd STKNM if is_bank
drop is_bank
egen n_stkcd = tag(stkcd)
count if n_stkcd==1
gen is_b = strpos(STKNM, "B") > 0
bysort stkcd: egen any_b = max(is_b)
keep if any_b == 0
drop is_b any_b
keep if year >= 2011 & year <= 2023
save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace
drop if MARKET == "北证A股"
save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace
count if n_stkcd==1
save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace
rename 绿色金融改革创新试验区 grfin
rename KZ指数 KZ
rename FC指数 FC
rename SA指数 SA
rename SA指数_abs SA_abs
rename WW指数 WW
save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace


import delimited "/Users/<USER>/Desktop/qhbqdx/中国城市控制变量2023（未插值）.csv"
rename 城市代码 CITYCODE
rename 年份 year
duplicates report CITYCODE
duplicates report CITYCODE year
drop 城镇户籍人口万人 非农业人口数万人 年平均人口万人 年末总户数万户 出生人口人 自然增长率 死亡人口人 城镇常住人口万人 常住人口城镇化率 城镇非私营单位从业人员数万人 城镇私营和个体从业人员数人 农林牧渔业从业人员数万人 采掘业从业人员数万人 制造业从业人员数万人 电力煤气及水生产供应业从业人员数万人 电力煤气及水生产供应业从业人员数万人 建筑业从业人员数万人 交通仓储邮电业从业人员数万人 信息传输计算机服务和软件业从业人员数万人 批发零售贸易业从业人员数万人 住宿餐饮业从业人员数万人 金融业从业人员数万人 房地产业从业人员数万人 租赁和商业服务业从业人员数万人 科研技术服务和地质勘查业从业人员数万人 水利环境和公共设施管理业从业人员数万人 居民服务和其他服务业从业人员数万人 教育业从业人员数万人 卫生社会保险和社会福利业从业人员数万人 文化体育和娱乐业从业人员数万人 公共管理和社会组织从业人员数万人 地质勘察水利管理业从业人数万人 交通仓储邮电通信业从业人员数万人 批发零售贸易餐饮业从业人员数万人 社会服务业从业人员数万人 卫生体育社会福利业从业人员数万人 教育文艺广播影视业从业人员数万人 科研综合技术服务业从业人员数万人 机关和社会团体从业人员数万人 城镇非私营单位在岗职工平均人数万人 城镇非私营单位在岗职工工资总额万元 城镇非私营单位在岗职工平均工资元 人均占有耕地面积亩 水资源总量万立方米 用水总量亿立方米 规模以上服务业营业收入万元 规模以上服务业营业收入增速 粮食产量万吨 油料产量万吨 棉花产量万吨 蔬菜产量吨 水果产量吨 肉类产量吨 奶类产量吨 水产品产量吨 禽蛋产量吨 内资企业工业总产值万元 港澳台商投资企业工业总产值万元 外商投资企业工业总产值万元 从业人员年平均人数万人 规模以上工业企业产品销售收入万元 规模以上工业企业利税总额万元 本年应交增值税万元 规模以上工业企业利润总额万元 流动资产年平均余额万元 固定资产净值年平均余额万元 规模以上工业企业资产总计万元 规模以上工业企业流动资产合计万元 规模以上工业企业固定资产合计万元 规模以上工业企业主营业务税金及附加万元 规模以上工业营业收入万元 规模以上工业营业收入增速 固定资产投资总额万元 房地产开发投资完成额万元 住宅开发投资完成额万元 社会消费品零售总额万元 限额以上单位商品零售额万元 限额以上批发零售业商品销售总额万元 限额以上批发零售业法人企业数个 当年新签项目合同个数个 当年合同外资金额万美元 当年实际使用外资金额万美元 货物进口额万元 货物出口额万元 地方财政一般预算内收入万元 工商税收万元 地方财政一般预算内支出万元 科学支出万元 教育支出万元 年末金融机构各项贷款余额万元 年末金融机构存款余额万元 城乡居民储蓄年末余额万元 普通高等学校学校数所 普通中学学校数所 小学学校数所 中等职业教育学校数所 普通高等学校专任教师数人 普通中学专任教师数人 小学专任教师数人 中等职业教育学校专任教师数人 普通高等学校在校学生数人 普通中学在校学生数万人 小学在校学生数万人 高中阶段在校学生数人 初中阶段学生数万人 普通高中学生数万人 学前教育学生数人 中等职业教育学校在校学生数人 成人高等学校在校学生数人 每万人在校大学生数人 每万人在校中等职业学生数人 rd人员人 rd内部经费支出万元 专利申请数件 专利授权数件 发明专利授权数件 高新技术企业数国家级个 技术合同成交额万元 公共图书馆个 剧场影剧院数个 公共图书馆图书总藏量千册件 每百人公共图书馆藏书册件 博物馆数个 体育场馆数个 卫生机构数个 医院卫生院数个 医院卫生院床位数张 执业助理医师数人 客运总量万人 铁路旅客运量万人 公路客运量万人 水运客运量万人 民用航空客运量人 城市公共交通客运总量万人次 客运量公路水路万人次 货运总量万吨 铁路货物运量万吨 公路货运量万吨 水运货运量万吨 民用航空货邮运量吨 货运量公路水路万吨 年末邮政局所数处 邮政业务收入万元 电信业务收入万元 本地电话年末用户数万户 移动电话年末用户数万户 国际互联网用户数户 全年用电量万千瓦时 工业用电万千瓦时 城乡居民生活用电量万千瓦时 城镇居民生活用电量万千瓦时 境内公路总里程公里 高速公路里程公里 工业废水排放量万吨 工业废水排放达标量万吨 工业二氧化硫去除量吨 工业二氧化硫排放量吨 工业烟尘去除量吨 工业烟尘排放量吨 工业二氧化硫产生量吨 工业烟粉尘去除量吨 工业烟粉尘排放量吨 工业氮氧化物排放量吨 工业颗粒物排放量吨 工业化学需氧量排放量吨 工业氨氮排放量吨 生活污水处理率 生活垃圾无害化处理率 工业固体废物综合利用率 一般工业固体废物综合利用率 污水处理厂集中处理率 细颗粒物pm25年平均浓度微克立方米 空气质量优良天数比例 保险业承保额万元 城镇职工基本养老保险参保人数人 职工基本医疗保险参保人数人 失业保险参保人数人 提供住宿的社会工作机构数个 养老机构数个 提供住宿的社会工作机构床位数张 养老机构床位数张 v214
drop 省份 城市 省份代码 所属地域 胡焕庸线
drop 常住人口万人
merge 1:m CITYCODE year using "/Users/<USER>/Desktop/clitansz/alldata.dta"
keep if _merge==3
drop _merge
save "/Users/<USER>/Desktop/clitansz/alldata4.dta"
drop if clipub ==.
drop 金融业 STPT AUDIT OPINION 长江经济带 经度 纬度 所属地域 hhyx
drop socialcontributionvalue donationamount certification certificationins gri shareholdersprotection creditorprotection staffprotection deliveryprotection customerprotection environmentprotection publicrelations systemconstruction worksafety deficiency ismandatorydisclose regulatorymodule big4
drop industrycode1 industryname1
drop enddate
drop source
drop totaltax
drop Symbol
drop declaredate
drop IndustryCode
drop IndustryName
drop EndDate
count if n_stkcd == 1
drop kdyear
drop sdcs
drop canjian
drop STKNM INDCD INDNM PROVCD PROVNM CITYCD CITYNM
label var sumshzr 企业社会责任
save "/Users/<USER>/Desktop/clitansz/alldata4.dta", replace






. use "/Users/<USER>/Desktop/clitansz/alldata8.dta"
(CSMAR)

. 
end of do-file

. drop Source StateTypeCode

. drop fatin

. drop Province LTD HTD ERD EED ClimatePhysicalRiskIndexCPR

. drop BroadbandChinaPolicy

. des

Contains data from /Users/<USER>/Desktop/clitansz/alldata8.dta
 Observations:        20,112                  CSMAR
    Variables:            92                  11 Jun 2025 14:08
----------------------------------------------------------------------------------------------------------------------------
Variable      Storage   Display    Value
    name         type    format    label      Variable label
----------------------------------------------------------------------------------------------------------------------------
stkcd           long    %10.0g                证券代码
RDPerson        double  %10.0g                研发人员数量
RDPersonRatio   double  %10.0g                研发人员数量占比(%)
RDSpendSum      double  %10.0g                研发投入金额
RDSpendSumRatio double  %10.0g                研发投入占营业收入比例(%)
RDExpenses      double  %10.0g                研发投入(支出)费用化的金额
RDInvest        double  %10.0g                研发投入(支出)资本化的金额
RDInvestRatio   double  %10.0g                资本化研发投入(支出)占研发投入的比例(%)
RDInvestNetpr~o double  %10.0g                资本化研发投入(支出)占当期净利润的比重(%)
year            float   %9.0g                 
省份            str24   %24s                  省份
城市            str21   %21s                  城市
CITYCODE        long    %10.0g                城市代码
qhsiyin         float   %9.0g                 
Prvcnm_id       long    %10.0g                省份代码
地区生产总值~元 long    %10.0g                地区生产总值(万元)
第一产业增加~元 long    %10.0g                第一产业增加值(万元)
第二产业增加~元 long    %10.0g                第二产业增加值(万元)
第三产业增加~元 long    %10.0g                第三产业增加值(万元)
第一产业增加~重 double  %10.0g                第一产业增加值占GDP比重(%)
第二产业增加~重 double  %10.0g                第二产业增加值占GDP比重(%)
第三产业增加~重 double  %10.0g                第三产业增加值占GDP比重(%)
人均地区生产~元 long    %10.0g                人均地区生产总值(元)
户籍人口万人    double  %10.0g                户籍人口(万人)
城镇非私营单~人 int     %10.0g                城镇非私营单位从业人员数(万人)
规模以上工业~个 int     %10.0g                规模以上工业企业数(个)
外商投资企业~个 int     %10.0g                外商投资企业数(个)
规模以上工业~元 long    %10.0g                规模以上工业企业利润总额(万元)
地方财政~入万元 long    %10.0g                地方财政一般预算内收入(万元)
地方财政~出万元 long    %10.0g                地方财政一般预算内支出(万元)
anuctd          double  %10.0g                企业当年总的被引用数
anuexfamctd     double  %10.0g                企业当年总的剔除自引的被引用数
anutoctd        double  %10.0g                企业对应年份时的总的被引用数
anutoexfamctd   double  %10.0g                企业对应年份时的总的剔除自引的被引用数
FC指数          double  %10.0g                融资约束，数值越大，企业面临的融资约束越大
WW指数          double  %10.0g                融资约束，数值越大，融资约束越大
KZ指数          double  %10.0g                融资约束，数值越大，企业面临的融资约束越大
SA指数          double  %10.0g                融资约束，数值越小，企业面临的融资约束越大
SA指数_abs      float   %9.0g                 SA指数的绝对值，数值越大，企业面临的融资约束越大
sumshzr         float   %9.0g                 企业社会责任
STKNM           str18   %18s                  股票中文名
INDCD           str12   %12s                  行业代码
INDNM           str60   %60s                  行业名称
PROVCD          str18   %18s                  省份代码
PROVNM          str24   %24s                  省份名称
CITYCD          str18   %18s                  所属城市代码_
CITYNM          str30   %30s                  所属城市_
MARKET          str16   %16s                  股票市场板块
STATE           str12   %12s                  上市状态_
AGE1            float   %9.0g                 从成立年份到观测年份的年龄
AGE2            float   %9.0g                 从上市年份到观测年份的年龄
BALANCE         float   %9.0g                 股权制衡度
BM1             double  %10.0g                账面市值比A_没有单位
BM2             double  %10.0g                账面市值比B_没有单位
BOARD           float   %9.0g                 董事规模
CFO1            float   %9.0g                 现金流状况-直接法
CFO2            float   %9.0g                 现金流状况-间接法
GROWTH          float   %9.0g                 营业收入增长率
INDBOARD        float   %9.0g                 独立董事占比
INSTITUTION1    double  %10.0g                机构投资者持股比例(相对总股本)_%
INSTITUTION2    double  %10.0g                机构投资者持股比例(相对流通A股)_%
LEV             float   %9.0g                 资产负债率
MFEE            float   %9.0g                 管理层费用率
MHOLD           float   %9.0g                 管理层持股比例
OCCUPY          float   %9.0g                 大股东资金占用
ROA             float   %9.0g                 总资产收益率
ROE             float   %9.0g                 净资产收益率
SIZE            float   %9.0g                 公司规模
TAT             float   %9.0g                 总资产周转率
TobinQ1         double  %10.0g                市值A除资产总计
TobinQ2         double  %10.0g                市值A除以括号资产总计减无形资产净额减商誉净额括号
TobinQ3         double  %10.0g                市值B除以资产总计
TobinQ4         double  %10.0g                市值B除以括号资产总计减无形资产净额减商誉净额括号
TOP1            float   %9.0g                 第一大股东持股数量
AUDIT           float   %9.0g                 是否由四大会计师事务所审计
DUAL            byte    %10.0g                两职合一
OPINION         float   %9.0g                 是否标准无保留意见
SOE             float   %9.0g                 是否为国企
CEmission       double  %10.0g                碳排放量
CemissionInte~y double  %10.0g                碳排放强调除10000
OperatingCost   double  %10.0g                营业成本
Revenue         double  %15.0f                营业收入
OperatingCost~u double  %10.0g                行业营业成本
EnergyConsuIndu double  %10.0g                行业能源消耗总量
yysr            float   %9.0g                 营业收入（百万元）
tanjx           float   %9.0g                 单位碳排放营业收入（百万元/吨）(碳绩效)
CITY            str33   %-33s                 所属城市
greenfin        byte    %10.0g                绿色金融改革创新试验区
city            str47   %47s                  
clipub          float   %9.0g                 Climate Risk Expressions of Public Views Index
tpfqd           float   %9.0g                 碳排放强度
province        str24   %24s                  省份
----------------------------------------------------------------------------------------------------------------------------
Sorted by: 
     Note: Dataset has changed since last saved.

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. * 基本信息类
. rename stkcd stockCode

. rename STKNM stockName

. rename INDCD industryCode

. rename INDNM industryName

. rename PROVCD provinceCode

. rename PROVNM provinceName

. rename CITYCD cityCode

. rename CITYNM cityName

. rename MARKET marketType

. rename STATE listingStatus

. 
end of do-file

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. * 研发相关变量
. rename RDPerson rdStaff

. rename RDPersonRatio rdStaffRatio

. rename RDSpendSum rdExpenditure

. rename RDSpendSumRatio rdIntensity

. rename RDExpenses rdExpensed

. rename RDInvest rdCapitalized

. rename RDInvestRatio rdCapitalizedRatio

. rename RDInvestNetpro rdCapitalizedToProfitRatio

. 
end of do-file

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. * 财务指标类
. rename LEV leverage

. rename ROA returnOnAssets

. rename ROE returnOnEquity

. rename SIZE firmSize

. rename TAT assetTurnover

. rename BM1 bookToMarket1

. rename BM2 bookToMarket2

. rename CFO1 cashFlowOps1

. rename CFO2 cashFlowOps2

. rename GROWTH revenueGrowth

. rename MFEE managementExpenseRatio

. rename MHOLD managerialOwnership

. rename OCCUPY controllingShareholderOccupancy

. rename TobinQ1 tobinsQ1

. rename TobinQ2 tobinsQ2

. rename TobinQ3 tobinsQ3

. rename TobinQ4 tobinsQ4

. 
end of do-file

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. * 公司治理类
. rename BOARD boardSize

. rename INDBOARD indDirectorRatio

. rename TOP1 largestShareHolding

. rename SOE stateOwned

. rename BALANCE ownershipBalance

. rename AUDIT bigFourAudit

. rename DUAL dualityRole

. rename OPINION standardAuditOpinion

. rename INSTITUTION1 institutionalOwnership1

. rename INSTITUTION2 institutionalOwnership2

. 
end of do-file

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. * 碳排放相关
. rename CEmission carbonEmission

. rename CemissionIntensity carbonIntensity

. rename tanjx carbonPerformance

. rename tpfqd carbonIntensityRegional

. 
end of do-file

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. * 创新产出
. rename anuctd citationTotal

. rename anuexfamctd citationExSelf

. rename anutoctd citationTotalCumulative

. rename anutoexfamctd citationExSelfCumulative

. 
end of do-file

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. 
. * 融资约束指标
. rename "FC指数" fcIndex
"FC指数 invalid name
r(198);

end of do-file

r(198);

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. * 区域经济指标
. rename 地区生产总值万元 gdpRegional

. rename 第一产业增加值万元 primaryIndustryValue

. rename 第二产业增加值万元 secondaryIndustryValue

. rename 第三产业增加值万元 tertiaryIndustryValue

. rename 第一产业增加值占GDP比重 primaryIndustryRatio

. rename 第二产业增加值占GDP比重 secondaryIndustryRatio

. rename 第三产业增加值占GDP比重 tertiaryIndustryRatio

. rename 人均地区生产总值元 gdpPerCapita

. rename 户籍人口万人 registeredPopulation

. rename 城镇非私营单位从业人员数 urbanEmployment

. rename 规模以上工业企业数 largeIndustrialEnterprises

. rename 外商投资企业数 foreignInvestedEnterprises

. rename 规模以上工业企业利润总额 largeIndustrialProfit

. rename 地方财政一般预算内收入 localFiscalRevenue

. rename 地方财政一般预算内支出 localFiscalExpenditure

. 
end of do-file

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. * 其他变量
. rename sumshzr csrScore

. rename AGE1 firmAge

. rename AGE2 listingAge

. rename OperatingCost operatingCost

. rename Revenue revenue

. rename OperatingCostIndu industryOperatingCost

. rename EnergyConsuIndu industryEnergyConsumption

. rename yysr operatingRevenue

. rename greenfin greenFinanceReform

. rename clipub climateRiskPublicView

. 
end of do-file

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD77736.000000"

. * 融资约束指标
. rename FC指数 fcIndex

. rename WW指数 wwIndex

. rename KZ指数 kzIndex

. rename SA指数 saIndex

. rename SA指数_abs saIndexAbs

. 
end of do-file

. drop provinceName cityCode cityName

. save "/Users/<USER>/Desktop/clitansz/alldata9.dta"
file /Users/<USER>/Desktop/clitansz/alldata9.dta saved

. 
