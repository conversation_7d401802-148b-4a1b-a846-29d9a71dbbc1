* 设置工作目录
cd "/Users/<USER>/Desktop/clipubtan"

* 使用原始数据
use "beiyin.dta", clear

* 首先按公司和年份汇总各项指标
collapse (sum) anuctd anuexfamctd anutoctd anutoexfamctd, by(stkcd year)

* 保存汇总后的数据
tempfile sumdata
save `sumdata'

* 获取每个企业最早和最晚的年份
bysort stkcd (year): egen firm_first_year = min(year)
bysort stkcd (year): egen firm_last_year = max(year)
keep stkcd firm_first_year firm_last_year
duplicates drop
tempfile firmyears
save `firmyears'

* 创建完整年份序列的面板数据
clear
set obs 1
gen year = 1996
expand 28  // 1996-2023共28年
replace year = 1996 + _n - 1 if _n > 1

* 与公司信息合并
cross using `firmyears'
sort stkcd year

* 只保留每个公司从1996到其最后观测年份的记录
drop if year > firm_last_year
drop firm_first_year firm_last_year

* 与汇总数据合并
merge 1:1 stkcd year using `sumdata', keep(match master) nogen

* 对缺失值进行处理（将缺失值替换为0）
foreach var of varlist anuctd anuexfamctd anutoctd anutoexfamctd {
    replace `var' = 0 if missing(`var')
}

* 标记变量
label variable anuctd "企业当年总的被引用数"
label variable anuexfamctd "企业当年总的剔除自引的被引用数"
label variable anutoctd "企业对应年份时的总的被引用数"
label variable anutoexfamctd "企业对应年份时的总的剔除自引的被引用数"

* 对数据进行排序
sort stkcd year

* 保存整理后的数据


* 显示整理后的数据基本信息
describe
sum

* 显示一些示例数据
list stkcd year anuctd anuexfamctd anutoctd anutoexfamctd if stkcd <= 5 & year <= 2000, sepby(stkcd) clean noobs