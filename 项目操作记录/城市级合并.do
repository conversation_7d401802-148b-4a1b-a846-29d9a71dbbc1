cd "/Users/<USER>/Desktop/clitansz/原始dta数据集"
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/市级年度气候公众表达.dta"
duplicates report CITYCODE year
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/绿色金融改革试点.dta"
duplicates report CITYCODE year
merge m:1 CITYCODE year using 市级年度气候公众表达.dta
replace 绿色金融改革创新试验区 = 0 if inlist(CITYCODE, 152200, 152500, 152900, 222400, 232700, 422800, 433100, 513200, 513300, 513400, 522300, 522600, 522700, 532300, 532500, 532600, 532800, 532900, 533100, 533300, 533400, 542500, 622900, 623000, 632200, 632300, 632600, 632700, 632800, 652700, 652800, 652900, 653000, 653100, 653200, 654000, 654200, 654300)
replace 绿色金融改革创新试验区 = 1 if CITYCODE == 652300
drop if _merge == 1
drop _merge
save "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金.dta"
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/环保法庭.dta"
duplicates report CITYCODE year
use "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金.dta"
duplicates report CITYCODE year
merge m:1 CITYCODE year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/环保法庭.dta"
duplicates report CITYCODE year
save "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金法庭.dta"
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/宽带中国.dta"
duplicates report CITYCODE year
rename 最早试点年份 kdyear
label var kdyear 宽带中国最早试点年份
rename DID kuandai
label var kuandai 宽带中国政策DID
drop K
rename 长江经济带 canjian
rename 试点城市 sdcs
rename 胡焕庸线 hhyx
save "/Users/<USER>/Desktop/clitansz/原始dta数据集/宽带中国.dta", replace
use "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金法庭.dta", clear
drop _merge
save "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金法庭.dta", replace
merge m:1 CITYCODE year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/宽带中国.dta"
save "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta"
