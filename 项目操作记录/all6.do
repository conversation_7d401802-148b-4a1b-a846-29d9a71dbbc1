use "/Users/<USER>/Desktop/clipubtan/alliv.dta"
use "/Users/<USER>/Desktop/clitansz/alldata5.dta"
use "/Users/<USER>/Desktop/clipubtan/alliv.dta"
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/alliv.dta"
duplicates report stkcd year
duplicates report CITYCODE year
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta"
 duplicates report stkcd year
drop enddate source declaredate province industrycode industryname totaltax industrycode1 industryname1 socialcontributionvalue donationamount certification certificationins gri shareholdersprotection creditorprotection staffprotection deliveryprotection customerprotection environmentprotection publicrelations systemconstruction worksafety deficiency ismandatorydisclose regulatorymodule big4
merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/alliv.dta"
drop if _merge==1
drop _merge
save "/Users/<USER>/Desktop/clitansz/新合并/ivshzr.dta"
drop 地区 所属地域 胡焕庸线 试点城市 最早试点年份
drop K
drop 所属省份 长江经济带 经度 纬度
drop minyear maxyear n should_have is_complete
gen tpfqd = CemissionIntensity * 10000
save "/Users/<USER>/Desktop/clitansz/新合并/ivshzr.dta", replace
drop ShortName
label var sumshzr 企业社会责任
label var STKNM 股票中文名
label var INDCD 行业代码
label var INDNM 行业名称
label var PROVCD 省份代码
label var PROVNM 省份名称
drop EndDate
label var tpfqd 碳排放强度
label var CemissionIntensity 碳排放强调除10000
duplicates report stkcd year
save "/Users/<USER>/Desktop/clitansz/新合并/ivshzr.dta", replace
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/融资约束.dta"
duplicates report stkcd year
drop IndustryCode IndustryName
drop Symbol ShortName
drop 金融业 STPT
merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/新合并/ivshzr.dta"
drop if _merge==1
drop _merge
save "/Users/<USER>/Desktop/clitansz/新合并/社会责任法庭.dta"
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/绿色专利被引.dta"
duplicates report stkcd year
 merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/新合并/社会责任法庭.dta"
drop if _merge==1
drop _merge
save "/Users/<USER>/Desktop/clitansz/新合并/社会责任法庭融资专利.dta"
import excel "/Users/<USER>/Downloads/中国城市数据库6.0版.xlsx", sheet("原始数据") firstrow clear
drop 农林牧渔业从业人员数万人 采掘业从业人员数万人 制造业从业人员数万人 电力煤气及水生产供应业从业人员数万人 建筑业从业人员数万人 交通仓储邮电业从业人员数万人 信息传输计算机服务和软件业从业人员数万人 批发零售贸易业从业人员数万人 住宿餐饮业从业人员数万人 金融业从业人员数万人 房地产业从业人员数万人 租赁和商业服务业从业人员数万人 科研技术服务和地质勘查业从业人员数万人 水利环境和公共设施管理业从业人员数万人 居民服务和其他服务业从业人员数万人 教育业从业人员数万人 卫生社会保险和社会福利业从业人员数万人 文化体育和娱乐业从业人员数万人 公共管理和社会组织从业人员数万人 地质勘察水利管理业从业人数万人 交通仓储邮电通信业从业人员数万人 批发零售贸易餐饮业从业人员数万人 社会服务业从业人员数万人 卫生体育社会福利业从业人员数万人 教育文艺广播影视业从业人员数万人 科研综合技术服务业从业人员数万人 机关和社会团体从业人员数万人 城镇非私营单位在岗职工平均人数万人 城镇非私营单位在岗职工工资总额万元 城镇非私营单位在岗职工平均工资元 行政区域土地面积平方公里 年末耕地总资源千公顷 人均占有耕地面积亩 人口密度人平方公里 水资源总量万立方米 用水总量亿立方米 地区生产总值增长率 规模以上服务业营业收入万元 规模以上服务业营业收入增速 粮食产量万吨 油料产量万吨 棉花产量万吨 蔬菜产量吨 水果产量吨 肉类产量吨 奶类产量吨 水产品产量吨 禽蛋产量吨
drop 固定资产投资总额万元 房地产开发投资完成额万元 住宅开发投资完成额万元 社会消费品零售总额万元 限额以上单位商品零售额万元 限额以上批发零售业商品销售总额万元 限额以上批发零售业法人企业数个 当年新签项目合同个数个 当年合同外资金额万美元 当年实际使用外资金额万美元 货物进口额万元 货物出口额万元 工商税收万元 科学支出万元 教育支出万元 年末金融机构各项贷款余额万元 年末金融机构存款余额万元 城乡居民储蓄年末余额万元 普通高等学校学校数所 普通中学学校数所 小学学校数所 中等职业教育学校数所 普通高等学校专任教师数人 普通中学专任教师数人 小学专任教师数人 中等职业教育学校专任教师数人 普通高等学校在校学生数人 普通中学在校学生数万人 小学在校学生数万人 高中阶段在校学生数人 初中阶段学生数万人 普通高中学生数万人 学前教育学生数人 中等职业教育学校在校学生数人 成人高等学校在校学生数人 每万人在校大学生数人 每万人在校中等职业学生数人 RD人员人 RD内部经费支出万元 专利申请数件 专利授权数件 发明专利授权数件 高新技术企业数国家级个 技术合同成交额万元 公共图书馆个 剧场影剧院数个 公共图书馆图书总藏量千册件 每百人公共图书馆藏书册件 博物馆数个 体育场馆数个 卫生机构数个 医院卫生院数个 医院卫生院床位数张 执业助理医师数人 客运总量万人 铁路旅客运量万人 公路客运量万人 水运客运量万人 民用航空客运量人 城市公共交通客运总量万人次 客运量公路水路万人次 货运总量万吨 铁路货物运量万吨 公路货运量万吨 水运货运量万吨 民用航空货邮运量吨 货运量公路水路万吨 年末邮政局所数处 邮政业务收入万元 电信业务收入万元 本地电话年末用户数万户 移动电话年末用户数万户 国际互联网用户数户 全年用电量万千瓦时 工业用电万千瓦时 城乡居民生活用电量万千瓦时 城镇居民生活用电量万千瓦时 境内公路总里程公里 高速公路里程公里 工业废水排放量万吨 工业废水排放达标量万吨 工业二氧化硫去除量吨 工业二氧化硫排放量吨 工业烟尘去除量吨 工业烟尘排放量吨 工业二氧化硫产生量吨 工业烟粉尘去除量吨 工业烟粉尘排放量吨 工业氮氧化物排放量吨 工业颗粒物排放量吨 工业化学需氧量排放量吨 工业氨氮排放量吨 生活污水处理率 生活垃圾无害化处理率 工业固体废物综合利用率 一般工业固体废物综合利用率 污水处理厂集中处理率 细颗粒物PM25年平均浓度微克立方米 空气质量优良天数比例 保险业承保额万元 城镇职工基本养老保险参保人数人 职工基本医疗保险参保人数人 失业保险参保人数人 提供住宿的社会工作机构数个 养老机构数个 提供住宿的社会工作机构床位数张 养老机构床位数张 HF
drop 本年应交增值税万元 流动资产年平均余额万元 固定资产净值年平均余额万元 规模以上工业企业资产总计万元 规模以上工业企业流动资产合计万元 规模以上工业企业固定资产合计万元 规模以上工业企业主营业务税金及附加万元 规模以上工业营业收入万元 规模以上工业营业收入增速
drop 胡焕庸线
drop 出生人口人 死亡人口人 自然增长率
drop 港澳台商投资企业数个
drop 内资企业工业总产值万元
drop 港澳台商投资企业工业总产值万元
drop 内资企业数个
drop 城镇常住人口万人 常住人口城镇化率
drop 常住人口万人
drop 年平均人口万人 年末总户数万户
drop 城镇户籍人口万人 非农业人口数万人
drop 所属地域
rename 年份 year
rename 城市代码 CITYCODE
duplicates report CITYCODE year
merge 1:m CITYCODE year using "/Users/<USER>/Desktop/clitansz/新合并/社会责任法庭融资专利.dta"
drop if _merge==1
drop _merge
save "/Users/<USER>/Desktop/clitansz/alldata6.dta"




use "/Users/<USER>/Desktop/clitansz/原始dta数据集/环保法庭.dta", clear
use "/Users/<USER>/Desktop/clitansz/alldata6.dta"
duplicates report CITYCODE year
use "/Users/<USER>/Desktop/clitansz/原始dta数据集/环保法庭.dta"
duplicates report CITYCODE year
use "/Users/<USER>/Desktop/clitansz/alldata6.dta"
merge m:1 CITYCODE year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/环保法庭.dta"
drop if _merge==2
drop _merge
save "/Users/<USER>/Desktop/clitansz/alldata6.dta", replace


drop 规模以上工业总产值当年价万元
drop 外商投资企业工业总产值万元 从业人员年平均人数万人 规模以上工业企业产品销售收入万元 规模以上工业企业利税总额万元
drop 城镇私营和个体从业人员数人 年末城镇登记失业人员数人 第一产业从业人员数万人 第二产业从业人员数万人 第三产业从业人员数万人 第一产业从业人员比重 第二产业从业人员比重 第三产业从业人员比重
drop NEIndustryCode NEIndustryName
drop I J
drop fatin_post fatin_treat
save "/Users/<USER>/Desktop/clitansz/alldata6.dta", replace

use "/Users/<USER>/Downloads/1652 气候适应型城市试点DID/气候适应型试点城市DID92005-2023).dta"

rename did qhsiyin 
drop post treat
rename 年份 year
rename 城市代码 CITYCODE
duplicates report CITYCODE year
merge 1:m CITYCODE year using "/Users/<USER>/Desktop/clitansz/alldata6.dta"
drop if _merge==1
drop _merge
save "/Users/<USER>/Desktop/clitansz/alldata7.dta"
