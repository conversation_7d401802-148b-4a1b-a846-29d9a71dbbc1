* 1. 载入数据并剔除所有变量缺失观测，保证样本一致性
use "/Users/<USER>/Desktop/clipubtan/alliv.dta", clear

drop if missing(clipub, DID, AGE1, AGE2, AUDIT, BALANCE, BM1, BM2, BOARD, CFO1, CFO2, DUAL, INDBOARD, GROWTH, INSTITUTION1, INSTITUTION2, LEV, MFEE, OCCUPY, OPINION, ROA, ROE, SIZE, SOE, TAT, TOP1, TobinQ1, TobinQ3, TobinQ2, TobinQ4, year, CemissionIntensity, stkcd)

* 2. 设置面板数据结构
xtset stkcd year

* 3. 生成年份虚拟变量（2012-2021）
tostring year, replace
foreach y of numlist 2012/2021 {
    gen year`y' = (year == "`y'")
}

* 4. clipub的缺失情况描述统计
misstable summarize clipub

* 5. 工具变量与内生解释变量clipub的相关性（第一阶段回归，双向固定效应）
xtreg clipub DID AGE1 AGE2 AUDIT BALANCE BM1 BM2 BOARD CFO1 CFO2 DUAL INDBOARD GROWTH INSTITUTION1 INSTITUTION2 LEV MFEE OCCUPY OPINION ROA ROE SIZE SOE TAT TOP1 TobinQ1 TobinQ3 TobinQ2 TobinQ4 year2012 year2013 year2014 year2015 year2016 year2017 year2018 year2019 year2020 year2021, fe

* 6. 工具变量与被解释变量CemissionIntensity的直接相关性（双向固定效应）
xtreg CemissionIntensity DID AGE1 AGE2 AUDIT BALANCE BM1 BM2 BOARD CFO1 CFO2 DUAL INDBOARD GROWTH INSTITUTION1 INSTITUTION2 LEV MFEE OCCUPY OPINION ROA ROE SIZE SOE TAT TOP1 TobinQ1 TobinQ3 TobinQ2 TobinQ4 year2012 year2013 year2014 year2015 year2016 year2017 year2018 year2019 year2020 year2021, fe

* 7. 工具变量法（2SLS）主回归及内生性检验（双向固定效应）
xtivreg2 CemissionIntensity (clipub = DID) AGE1 AGE2 AUDIT BALANCE BM1 BM2 BOARD CFO1 CFO2 DUAL INDBOARD GROWTH INSTITUTION1 INSTITUTION2 LEV MFEE OCCUPY OPINION ROA ROE SIZE SOE TAT TOP1 TobinQ1 TobinQ3 TobinQ2 TobinQ4 year2012 year2013 year2014 year2015 year2016 year2017 year2018 year2019 year2020 year2021, fe robust first endog(clipub)

* 8. 还原year为数值型（如需后续分析）
destring year, replace

* 9. （可选）xtreg的i.year版本，直接用因子变量控制年份固定效应
xtreg clipub DID AGE1 AGE2 AUDIT BALANCE BM1 BM2 BOARD CFO1 CFO2 DUAL INDBOARD GROWTH INSTITUTION1 INSTITUTION2 LEV MFEE OCCUPY OPINION ROA ROE SIZE SOE TAT TOP1 TobinQ1 TobinQ3 TobinQ2 TobinQ4 i.year, fe

xtreg CemissionIntensity DID AGE1 AGE2 AUDIT BALANCE BM1 BM2 BOARD CFO1 CFO2 DUAL INDBOARD GROWTH INSTITUTION1 INSTITUTION2 LEV MFEE OCCUPY OPINION ROA ROE SIZE SOE TAT TOP1 TobinQ1 TobinQ3 TobinQ2 TobinQ4 i.year, fe

* 10. （可选）xtivreg2的i.year版本（注意：xtivreg2不支持i.year，运行会报错，仅供对比）
* xtivreg2 CemissionIntensity (clipub = DID) AGE1 AGE2 AUDIT BALANCE BM1 BM2 BOARD CFO1 CFO2 DUAL INDBOARD GROWTH INSTITUTION1 INSTITUTION2 LEV MFEE OCCUPY OPINION ROA ROE SIZE SOE TAT TOP1 TobinQ1 TobinQ3 TobinQ2 TobinQ4 i.year, fe robust first endog(clipub)
