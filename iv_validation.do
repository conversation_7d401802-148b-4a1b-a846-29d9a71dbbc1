/*==============================================================================
* 文件名: iv_validation.do
* 功能: 工具变量有效性验证和诊断
* 配合主分析文件: climate_risk_iv_analysis.do
* 日期: 2025年1月
*==============================================================================*/

clear all
set more off

* 设置工作目录
cd "/Users/<USER>/Desktop/clitansz"

* 加载包含工具变量的数据
use "alldata9_with_iv.dta", clear

/*==============================================================================
* 第一部分：工具变量相关性检验（第一阶段）
*==============================================================================*/

* 定义控制变量
global firm_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity"
global region_controls "secondaryIndustryRatio gdpPerCapita"
global all_controls "$firm_controls $region_controls"

* 1.1 单个工具变量的第一阶段回归
* 城市工具变量
reghdfe climateRiskPublicView iv_city_other_mean $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store first_city
test iv_city_other_mean
local f_city = r(F)
display "城市工具变量第一阶段F统计量: " `f_city'

* 省份工具变量
reghdfe climateRiskPublicView iv_prov_other_mean $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store first_prov
test iv_prov_other_mean
local f_prov = r(F)
display "省份工具变量第一阶段F统计量: " `f_prov'

* 行业工具变量
reghdfe climateRiskPublicView iv_ind_other_mean $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store first_ind
test iv_ind_other_mean
local f_ind = r(F)
display "行业工具变量第一阶段F统计量: " `f_ind'

* 滞后工具变量
reghdfe climateRiskPublicView iv_climate_lag2 $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store first_lag
test iv_climate_lag2
local f_lag = r(F)
display "滞后工具变量第一阶段F统计量: " `f_lag'

* 1.2 多个工具变量的联合第一阶段回归
reghdfe climateRiskPublicView iv_city_other_mean iv_prov_other_mean iv_climate_lag2 $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store first_combined
test iv_city_other_mean iv_prov_other_mean iv_climate_lag2
local f_combined = r(F)
display "组合工具变量第一阶段F统计量: " `f_combined'

* 输出第一阶段回归结果
esttab first_city first_prov first_ind first_lag first_combined, ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("第一阶段回归：工具变量相关性检验") ///
    mtitles("城市IV" "省份IV" "行业IV" "滞后IV" "组合IV") ///
    keep(iv_city_other_mean iv_prov_other_mean iv_ind_other_mean iv_climate_lag2) ///
    scalars("N 观测值" "r2 R-squared" "F F统计量")

/*==============================================================================
* 第二部分：工具变量外生性检验
*==============================================================================*/

* 2.1 工具变量与因变量的直接相关性检验（应该不显著）
* 城市工具变量
reghdfe carbonIntensityRegional iv_city_other_mean $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
test iv_city_other_mean
local p_city_direct = r(p)
display "城市工具变量直接影响检验p值: " `p_city_direct'

* 省份工具变量
reghdfe carbonIntensityRegional iv_prov_other_mean $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
test iv_prov_other_mean
local p_prov_direct = r(p)
display "省份工具变量直接影响检验p值: " `p_prov_direct'

* 行业工具变量
reghdfe carbonIntensityRegional iv_ind_other_mean $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
test iv_ind_other_mean
local p_ind_direct = r(p)
display "行业工具变量直接影响检验p值: " `p_ind_direct'

* 滞后工具变量
reghdfe carbonIntensityRegional iv_climate_lag2 $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
test iv_climate_lag2
local p_lag_direct = r(p)
display "滞后工具变量直接影响检验p值: " `p_lag_direct'

* 2.2 工具变量与控制变量的相关性检验
* 检验工具变量是否与企业特征相关（理想情况下应该不相关）
pwcorr iv_city_other_mean iv_prov_other_mean iv_ind_other_mean iv_climate_lag2 ///
    firmSize firmAge leverage stateOwned, star(0.05) sig

/*==============================================================================
* 第三部分：工具变量强度检验
*==============================================================================*/

* 3.1 Stock-Yogo弱工具变量检验临界值对比
display "=== 弱工具变量检验结果 ==="
display "Stock-Yogo临界值（10% maximal IV size）: 16.38"
display "城市工具变量F统计量: " `f_city'
display "省份工具变量F统计量: " `f_prov'
display "行业工具变量F统计量: " `f_ind'
display "滞后工具变量F统计量: " `f_lag'
display "组合工具变量F统计量: " `f_combined'

* 判断是否通过弱工具变量检验
if `f_city' > 16.38 {
    display "城市工具变量通过弱工具变量检验"
}
else {
    display "城市工具变量未通过弱工具变量检验"
}

if `f_prov' > 16.38 {
    display "省份工具变量通过弱工具变量检验"
}
else {
    display "省份工具变量未通过弱工具变量检验"
}

if `f_ind' > 16.38 {
    display "行业工具变量通过弱工具变量检验"
}
else {
    display "行业工具变量未通过弱工具变量检验"
}

if `f_lag' > 16.38 {
    display "滞后工具变量通过弱工具变量检验"
}
else {
    display "滞后工具变量未通过弱工具变量检验"
}

if `f_combined' > 16.38 {
    display "组合工具变量通过弱工具变量检验"
}
else {
    display "组合工具变量未通过弱工具变量检验"
}

/*==============================================================================
* 第四部分：过度识别检验
*==============================================================================*/

* 4.1 使用多个工具变量进行过度识别检验
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean iv_prov_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE)
estat overid
local hansen_p = r(p)
display "Hansen J统计量p值（城市+省份工具变量）: " `hansen_p'

ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean iv_climate_lag2), ///
    absorb(stockCode year) cluster(CITYCODE)
estat overid
local hansen_p2 = r(p)
display "Hansen J统计量p值（城市+滞后工具变量）: " `hansen_p2'

ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean iv_prov_other_mean iv_climate_lag2), ///
    absorb(stockCode year) cluster(CITYCODE)
estat overid
local hansen_p3 = r(p)
display "Hansen J统计量p值（三个工具变量组合）: " `hansen_p3'

/*==============================================================================
* 第五部分：内生性检验
*==============================================================================*/

* 5.1 Durbin-Wu-Hausman内生性检验
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE)
estat endogenous
local endog_p = r(p)
display "内生性检验p值: " `endog_p'

if `endog_p' < 0.05 {
    display "拒绝外生性假设，存在内生性问题，需要使用工具变量"
}
else {
    display "不能拒绝外生性假设，可能不存在内生性问题"
}

/*==============================================================================
* 第六部分：工具变量诊断报告
*==============================================================================*/

* 生成工具变量诊断报告
file open report using "iv_diagnostic_report.txt", write replace
file write report "工具变量诊断报告" _n
file write report "==================" _n
file write report "分析时间: " c(current_date) " " c(current_time) _n
file write report "" _n

file write report "一、工具变量相关性检验（第一阶段F统计量）" _n
file write report "城市工具变量: " `f_city' _n
file write report "省份工具变量: " `f_prov' _n
file write report "行业工具变量: " `f_ind' _n
file write report "滞后工具变量: " `f_lag' _n
file write report "组合工具变量: " `f_combined' _n
file write report "Stock-Yogo临界值: 16.38" _n
file write report "" _n

file write report "二、工具变量外生性检验（直接影响p值）" _n
file write report "城市工具变量: " `p_city_direct' _n
file write report "省份工具变量: " `p_prov_direct' _n
file write report "行业工具变量: " `p_ind_direct' _n
file write report "滞后工具变量: " `p_lag_direct' _n
file write report "注：p值应大于0.05，表示工具变量不直接影响因变量" _n
file write report "" _n

file write report "三、过度识别检验（Hansen J统计量p值）" _n
file write report "城市+省份工具变量: " `hansen_p' _n
file write report "城市+滞后工具变量: " `hansen_p2' _n
file write report "三个工具变量组合: " `hansen_p3' _n
file write report "注：p值应大于0.05，表示工具变量满足外生性条件" _n
file write report "" _n

file write report "四、内生性检验（Durbin-Wu-Hausman检验p值）" _n
file write report "内生性检验p值: " `endog_p' _n
file write report "注：p值小于0.05表示存在内生性，需要使用工具变量" _n
file write report "" _n

file write report "五、工具变量推荐" _n
if `f_city' > 16.38 & `p_city_direct' > 0.05 {
    file write report "推荐使用：城市工具变量（通过相关性和外生性检验）" _n
}
if `f_prov' > 16.38 & `p_prov_direct' > 0.05 {
    file write report "推荐使用：省份工具变量（通过相关性和外生性检验）" _n
}
if `f_ind' > 16.38 & `p_ind_direct' > 0.05 {
    file write report "推荐使用：行业工具变量（通过相关性和外生性检验）" _n
}
if `f_lag' > 16.38 & `p_lag_direct' > 0.05 {
    file write report "推荐使用：滞后工具变量（通过相关性和外生性检验）" _n
}

file close report

display "工具变量诊断完成！"
display "诊断报告已保存为: iv_diagnostic_report.txt"

/*==============================================================================
* 第七部分：可视化工具变量分布
*==============================================================================*/

* 工具变量与内生变量的散点图
scatter climateRiskPublicView iv_city_other_mean, ///
    title("公众气候风险表达 vs 城市工具变量") ///
    xtitle("城市其他企业气候风险表达均值") ///
    ytitle("企业气候风险表达") ///
    note("相关系数应显著为正")
graph export "iv_city_scatter.png", replace

scatter climateRiskPublicView iv_prov_other_mean, ///
    title("公众气候风险表达 vs 省份工具变量") ///
    xtitle("省份其他企业气候风险表达均值") ///
    ytitle("企业气候风险表达") ///
    note("相关系数应显著为正")
graph export "iv_prov_scatter.png", replace

display "散点图已保存："
display "- iv_city_scatter.png"
display "- iv_prov_scatter.png"
