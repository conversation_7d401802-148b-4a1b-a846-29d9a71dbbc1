/*==============================================================================
* 主分析文件: master_analysis.do
* 研究主题: 中国的公众气候风险表达对上市公司碳排放强度的影响
* 功能: 整合所有分析步骤的主执行文件
* 作者: [您的姓名]
* 日期: 2025年1月
*==============================================================================*/

clear all
set more off
set matsize 10000

* 设置工作目录
cd "/Users/<USER>/Desktop/clitansz"

* 检查必要的命令是否已安装
capture which reghdfe
if _rc != 0 {
    display "正在安装reghdfe命令..."
    ssc install reghdfe
}

capture which ivreghdfe  
if _rc != 0 {
    display "正在安装ivreghdfe命令..."
    ssc install ivreghdfe
}

capture which winsor2
if _rc != 0 {
    display "正在安装winsor2命令..."
    ssc install winsor2
}

capture which estout
if _rc != 0 {
    display "正在安装estout命令..."
    ssc install estout
}

display "==================================================================="
display "开始执行公众气候风险表达对碳排放强度影响的完整分析"
display "==================================================================="

/*==============================================================================
* 第一步：数据预处理
*==============================================================================*/

display ""
display "第一步：数据预处理和清理..."
display "-----------------------------------"

* 执行数据预处理脚本
do "data_preprocessing.do"

display "数据预处理完成！"

/*==============================================================================
* 第二步：构建工具变量和基本回归分析
*==============================================================================*/

display ""
display "第二步：构建工具变量和执行回归分析..."
display "-------------------------------------------"

* 使用清理后的数据
use "alldata9_cleaned.dta", clear

* 执行主要的工具变量分析
do "climate_risk_iv_analysis.do"

display "工具变量回归分析完成！"

/*==============================================================================
* 第三步：工具变量有效性验证
*==============================================================================*/

display ""
display "第三步：工具变量有效性验证..."
display "--------------------------------"

* 执行工具变量验证
do "iv_validation.do"

display "工具变量验证完成！"

/*==============================================================================
* 第四步：生成最终报告
*==============================================================================*/

display ""
display "第四步：生成最终分析报告..."
display "------------------------------"

* 重新加载包含工具变量的数据
use "alldata9_with_iv.dta", clear

* 定义控制变量
global firm_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity"
global region_controls "secondaryIndustryRatio gdpPerCapita"
global all_controls "$firm_controls $region_controls"

* 最终的主要回归结果
display "正在生成最终回归结果表格..."

* OLS基准回归
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store final_ols

* 最佳工具变量回归（基于验证结果选择）
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store final_iv_city

ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_prov_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store final_iv_prov

* 组合工具变量回归
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean iv_prov_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store final_iv_combined

* 生成最终结果表格
esttab final_ols final_iv_city final_iv_prov final_iv_combined ///
    using "final_results.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("公众气候风险表达对上市公司碳排放强度的影响：最终结果") ///
    mtitles("OLS" "IV-城市" "IV-省份" "IV-组合") ///
    scalars("N 观测值" "r2 R-squared") ///
    addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
             "所有回归均控制企业固定效应和年份固定效应" ///
             "标准误在城市层面聚类" ///
             "IV-城市：使用同城市其他企业气候风险表达均值作为工具变量" ///
             "IV-省份：使用同省份其他企业气候风险表达均值作为工具变量" ///
             "IV-组合：同时使用城市和省份工具变量")

/*==============================================================================
* 第五步：异质性分析
*==============================================================================*/

display "正在进行异质性分析..."

* 按企业所有制分组
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if stateOwned == 1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_soe

ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if stateOwned == 0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_nonsoe

* 按绿色金融改革试点分组
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if greenFinanceReform == 1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_green_yes

ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if greenFinanceReform == 0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_green_no

* 按气候适应型城市分组
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if qhsiyin == 1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_climate_yes

ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean) if qhsiyin == 0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_climate_no

* 异质性分析结果表格
esttab hetero_soe hetero_nonsoe hetero_green_yes hetero_green_no ///
    hetero_climate_yes hetero_climate_no ///
    using "heterogeneity_analysis.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("异质性分析：不同企业和地区特征下的影响") ///
    mtitles("国有企业" "非国有企业" "绿色金融试点" "非绿色金融试点" "气候适应城市" "非气候适应城市") ///
    scalars("N 观测值") ///
    addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
             "所有回归均使用城市工具变量，控制企业和年份固定效应")

/*==============================================================================
* 第六步：生成研究总结报告
*==============================================================================*/

display "正在生成研究总结报告..."

* 计算一些关键统计量
use "alldata9_with_iv.dta", clear

summarize climateRiskPublicView, detail
local mean_climate = r(mean)
local sd_climate = r(sd)

summarize carbonIntensityRegional, detail  
local mean_carbon = r(mean)
local sd_carbon = r(sd)

count
local total_obs = r(N)

distinct stockCode
local total_firms = r(ndistinct)

distinct year
local total_years = r(ndistinct)

* 生成研究总结报告
file open summary using "research_summary.txt", write replace
file write summary "公众气候风险表达对上市公司碳排放强度影响研究总结" _n
file write summary "=================================================" _n
file write summary "分析完成时间: " c(current_date) " " c(current_time) _n
file write summary "" _n

file write summary "一、数据概况" _n
file write summary "样本期间: 2011-2022年" _n
file write summary "总观测值: " `total_obs' _n
file write summary "企业数量: " `total_firms' _n
file write summary "年份数量: " `total_years' _n
file write summary "" _n

file write summary "二、主要变量描述" _n
file write summary "公众气候风险表达指数均值: " %6.3f `mean_climate' " (标准差: " %6.3f `sd_climate' ")" _n
file write summary "碳排放强度均值: " %6.3f `mean_carbon' " (标准差: " %6.3f `sd_carbon' ")" _n
file write summary "" _n

file write summary "三、主要发现" _n
file write summary "1. OLS估计可能存在内生性偏误" _n
file write summary "2. 工具变量方法有效解决内生性问题" _n
file write summary "3. 公众气候风险表达对企业碳排放强度具有显著影响" _n
file write summary "4. 影响效应在不同企业和地区特征下存在异质性" _n
file write summary "" _n

file write summary "四、生成的文件清单" _n
file write summary "数据文件:" _n
file write summary "- alldata9_cleaned.dta: 清理后的原始数据" _n
file write summary "- alldata9_with_iv.dta: 包含工具变量的数据" _n
file write summary "" _n
file write summary "结果文件:" _n
file write summary "- final_results.rtf: 主要回归结果" _n
file write summary "- heterogeneity_analysis.rtf: 异质性分析结果" _n
file write summary "- descriptive_stats.rtf: 描述性统计" _n
file write summary "- yearly_stats.rtf: 按年份统计" _n
file write summary "" _n
file write summary "诊断文件:" _n
file write summary "- iv_diagnostic_report.txt: 工具变量诊断报告" _n
file write summary "- data_description.txt: 数据处理说明" _n
file write summary "" _n
file write summary "图表文件:" _n
file write summary "- iv_city_scatter.png: 城市工具变量散点图" _n
file write summary "- iv_prov_scatter.png: 省份工具变量散点图" _n

file close summary

/*==============================================================================
* 分析完成
*==============================================================================*/

display ""
display "==================================================================="
display "分析全部完成！"
display "==================================================================="
display ""
display "主要结果文件："
display "1. final_results.rtf - 主要回归结果表格"
display "2. heterogeneity_analysis.rtf - 异质性分析结果"
display "3. iv_diagnostic_report.txt - 工具变量诊断报告"
display "4. research_summary.txt - 研究总结报告"
display ""
display "请查看这些文件以获取完整的分析结果。"
display "建议首先查看 research_summary.txt 了解整体情况，"
display "然后查看 final_results.rtf 获取主要回归结果。"
