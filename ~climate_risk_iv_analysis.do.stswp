/*==============================================================================
* 文件名: climate_risk_iv_analysis.do
* 研究主题: 中国的公众气候风险表达对上市公司碳排放强度的影响
* 功能: 完整的工具变量分析（数据预处理+工具变量构建+回归分析+验证）
* 作者: [您的姓名]
* 日期: 2025年1月
* 数据: alldata9.dta (2011-2022年面板数据)
*==============================================================================*/

clear all
set more off
set matsize 10000

* 设置工作目录和数据路径
cd "/Users/<USER>/Desktop/clitansz"

* 检查并安装必要的命令
capture which reghdfe
if _rc != 0 {
    display "正在安装reghdfe命令..."
    ssc install reghdfe
}

capture which ivreghdfe
if _rc != 0 {
    display "正在安装ivreghdfe命令..."
    ssc install ivreghdfe
}

capture which winsor2
if _rc != 0 {
    display "正在安装winsor2命令..."
    ssc install winsor2
}

capture which estout
if _rc != 0 {
    display "正在安装estout命令..."
    ssc install estout
}

display "==================================================================="
display "开始执行公众气候风险表达对碳排放强度影响的完整分析"
display "==================================================================="

* 加载数据
use "alldata9.dta", clear

* 立即排序数据
sort stockCode year

/*==============================================================================
* 第一部分：数据预处理和缩尾处理
*==============================================================================*/

display ""
display "第一步：数据预处理和缩尾处理..."
display "-----------------------------------"

* 对主要连续变量进行1%和99%分位数缩尾处理
* 核心变量
winsor2 climateRiskPublicView, replace cuts(1 99)
winsor2 carbonIntensityRegional, replace cuts(1 99)

* 企业财务变量
winsor2 firmSize, replace cuts(1 99)
winsor2 firmAge, replace cuts(1 99)
winsor2 leverage, replace cuts(1 99)
winsor2 ownershipBalance, replace cuts(1 99)
winsor2 tobinsQ1, replace cuts(1 99)
winsor2 returnOnEquity, replace cuts(1 99)
winsor2 returnOnAssets, replace cuts(1 99)

* 研发相关变量
winsor2 rdIntensity, replace cuts(1 99)
winsor2 rdExpenditure, replace cuts(1 99)
winsor2 rdStaff, replace cuts(1 99)
winsor2 rdStaffRatio, replace cuts(1 99)

* 创新质量变量
winsor2 citationExSelfCumulative, replace cuts(1 99)
winsor2 citationTotalCumulative, replace cuts(1 99)

* 企业社会责任
winsor2 csrScore, replace cuts(1 99)

* 地区经济变量
winsor2 gdpPerCapita, replace cuts(1 99)
winsor2 secondaryIndustryRatio, replace cuts(1 99)
winsor2 gdpRegional, replace cuts(1 99)

* 融资约束指标
winsor2 fcIndex, replace cuts(1 99)
winsor2 wwIndex, replace cuts(1 99)
winsor2 kzIndex, replace cuts(1 99)
winsor2 saIndex, replace cuts(1 99)

* 删除关键变量缺失的观测值
drop if missing(stockCode) | missing(year)
drop if missing(climateRiskPublicView) | missing(carbonIntensityRegional)
drop if missing(industryCode) | missing(Prvcnm_id) | missing(CITYCODE)

* 保留主要分析期间的数据
keep if year >= 2011 & year <= 2022

* 确保数据按面板结构排序
sort stockCode year

display "数据预处理完成！"

/*==============================================================================
* 第二部分：基本设置和变量标签
*==============================================================================*/

* 设置面板数据结构（先排序再设置）
sort stockCode year
xtset stockCode year

* 生成基本变量标签（中文注释）
label variable stockCode "股票代码"
label variable year "年份"
label variable climateRiskPublicView "公众气候风险表达指数"
label variable carbonIntensityRegional "碳排放强度"
label variable industryCode "行业代码"
label variable Prvcnm_id "省份代码"
label variable CITYCODE "城市代码"

* 生成辅助变量
gen ln_firmAge = ln(firmAge + 1)
label variable ln_firmAge "企业年龄对数"

gen ln_gdpPerCapita = ln(gdpPerCapita)
label variable ln_gdpPerCapita "人均GDP对数"

* 确保企业所有制为0-1变量
replace stateOwned = 1 if stateOwned > 0 & !missing(stateOwned)
replace stateOwned = 0 if stateOwned == 0
label variable stateOwned "国有企业虚拟变量"

* 检查关键变量的缺失值情况
summarize climateRiskPublicView carbonIntensityRegional industryCode Prvcnm_id CITYCODE

display "基本设置完成！"

/*==============================================================================
* 第三部分：构建地理/区域聚合工具变量
*==============================================================================*/

display ""
display "第二步：构建工具变量..."
display "------------------------"

* 1. 同年度同城市其他企业的公众气候风险表达均值
bysort CITYCODE year: egen temp_city_mean = mean(climateRiskPublicView)
bysort CITYCODE year: egen temp_city_count = count(climateRiskPublicView)
* 只有当城市内企业数量大于1时才构建工具变量
gen iv_city_other_mean = (temp_city_mean * temp_city_count - climateRiskPublicView) / (temp_city_count - 1) if temp_city_count > 1
replace iv_city_other_mean = . if temp_city_count <= 1
* 检查是否存在完全相关的情况，如果是则设为缺失
replace iv_city_other_mean = . if abs(iv_city_other_mean - climateRiskPublicView) < 0.001
drop temp_city_mean temp_city_count
label variable iv_city_other_mean "同城市其他企业气候风险表达均值"

* 检查工具变量的有效性
count if !missing(iv_city_other_mean)
display "城市工具变量非缺失观测数: " r(N)
count if !missing(iv_city_other_mean) & !missing(climateRiskPublicView)
if r(N) > 0 {
    pwcorr climateRiskPublicView iv_city_other_mean, sig
    display "城市工具变量与内生变量的相关系数已显示"
}
else {
    display "城市工具变量无有效观测，跳过相关性检验"
}

* 2. 同年度同省份其他企业的公众气候风险表达均值
bysort Prvcnm_id year: egen temp_prov_mean = mean(climateRiskPublicView)
bysort Prvcnm_id year: egen temp_prov_count = count(climateRiskPublicView)
gen iv_prov_other_mean = (temp_prov_mean * temp_prov_count - climateRiskPublicView) / (temp_prov_count - 1) if temp_prov_count > 1
replace iv_prov_other_mean = . if temp_prov_count <= 1
drop temp_prov_mean temp_prov_count
label variable iv_prov_other_mean "同省份其他企业气候风险表达均值"

* 检查省份工具变量的有效性
count if !missing(iv_prov_other_mean)
display "省份工具变量非缺失观测数: " r(N)
pwcorr climateRiskPublicView iv_prov_other_mean, sig
display "省份工具变量与内生变量的相关系数已显示"

* 3. 同年度同城市同行业其他企业的公众气候风险表达均值
bysort CITYCODE industryCode year: egen temp_city_ind_mean = mean(climateRiskPublicView)
bysort CITYCODE industryCode year: egen temp_city_ind_count = count(climateRiskPublicView)
gen iv_city_ind_other_mean = (temp_city_ind_mean * temp_city_ind_count - climateRiskPublicView) / (temp_city_ind_count - 1) if temp_city_ind_count > 1
replace iv_city_ind_other_mean = . if temp_city_ind_count <= 1
drop temp_city_ind_mean temp_city_ind_count
label variable iv_city_ind_other_mean "同城市同行业其他企业气候风险表达均值"

* 4. 同年度同省份同行业其他企业的公众气候风险表达均值
bysort Prvcnm_id industryCode year: egen temp_prov_ind_mean = mean(climateRiskPublicView)
bysort Prvcnm_id industryCode year: egen temp_prov_ind_count = count(climateRiskPublicView)
gen iv_prov_ind_other_mean = (temp_prov_ind_mean * temp_prov_ind_count - climateRiskPublicView) / (temp_prov_ind_count - 1) if temp_prov_ind_count > 1
replace iv_prov_ind_other_mean = . if temp_prov_ind_count <= 1
drop temp_prov_ind_mean temp_prov_ind_count
label variable iv_prov_ind_other_mean "同省份同行业其他企业气候风险表达均值"

/*==============================================================================
* 第四部分：构建行业聚合工具变量
*==============================================================================*/

* 5. 同年度同行业其他企业的公众气候风险表达均值（全国层面）
bysort industryCode year: egen temp_ind_mean = mean(climateRiskPublicView)
bysort industryCode year: egen temp_ind_count = count(climateRiskPublicView)
gen iv_ind_other_mean = (temp_ind_mean * temp_ind_count - climateRiskPublicView) / (temp_ind_count - 1) if temp_ind_count > 1
replace iv_ind_other_mean = . if temp_ind_count <= 1
drop temp_ind_mean temp_ind_count
label variable iv_ind_other_mean "同行业其他企业气候风险表达均值"

* 6. 同年度同行业不同省份企业的公众气候风险表达均值
bysort industryCode Prvcnm_id year: egen temp_ind_prov_mean = mean(climateRiskPublicView)
bysort industryCode year: egen temp_ind_total_mean = mean(climateRiskPublicView)
bysort industryCode Prvcnm_id year: egen temp_ind_prov_count = count(climateRiskPublicView)
bysort industryCode year: egen temp_ind_total_count = count(climateRiskPublicView)
gen iv_ind_other_prov_mean = (temp_ind_total_mean * temp_ind_total_count - temp_ind_prov_mean * temp_ind_prov_count) / (temp_ind_total_count - temp_ind_prov_count) if temp_ind_total_count > temp_ind_prov_count
replace iv_ind_other_prov_mean = . if temp_ind_total_count <= temp_ind_prov_count
drop temp_ind_prov_mean temp_ind_total_mean temp_ind_prov_count temp_ind_total_count
label variable iv_ind_other_prov_mean "同行业不同省份企业气候风险表达均值"

/*==============================================================================
* 第五部分：构建滞后期工具变量
*==============================================================================*/

* 确保数据正确排序以生成滞后变量
sort stockCode year
xtset stockCode year

* 7. 公众气候风险表达滞后1期
gen iv_climate_lag1 = L.climateRiskPublicView
label variable iv_climate_lag1 "公众气候风险表达滞后1期"

* 8. 公众气候风险表达滞后2期
gen iv_climate_lag2 = L2.climateRiskPublicView
label variable iv_climate_lag2 "公众气候风险表达滞后2期"

* 9. 公众气候风险表达滞后3期
gen iv_climate_lag3 = L3.climateRiskPublicView
label variable iv_climate_lag3 "公众气候风险表达滞后3期"

* 10. 企业历史气候风险表达均值（2011-2013年基期）
bysort stockCode: egen temp_hist_mean = mean(climateRiskPublicView) if year >= 2011 & year <= 2013
bysort stockCode: egen iv_hist_mean = max(temp_hist_mean)
replace iv_hist_mean = . if year <= 2013  // 只在2014年之后使用
drop temp_hist_mean
label variable iv_hist_mean "企业历史气候风险表达均值(2011-2013)"

* 11. 城市历史气候风险表达均值（2011-2013年基期）
bysort CITYCODE: egen temp_city_hist_mean = mean(climateRiskPublicView) if year >= 2011 & year <= 2013
bysort CITYCODE: egen iv_city_hist_mean = max(temp_city_hist_mean)
replace iv_city_hist_mean = . if year <= 2013
drop temp_city_hist_mean
label variable iv_city_hist_mean "城市历史气候风险表达均值(2011-2013)"

/*==============================================================================
* 第六部分：定义控制变量和描述性统计
*==============================================================================*/

* 构建额外的工具变量以增强识别
* 12. 地级市层面的工具变量（不排除本企业）
bysort CITYCODE year: egen iv_city_mean_all = mean(climateRiskPublicView)
label variable iv_city_mean_all "城市层面气候风险表达均值"

* 13. 省份层面的工具变量（不排除本企业）
bysort Prvcnm_id year: egen iv_prov_mean_all = mean(climateRiskPublicView)
label variable iv_prov_mean_all "省份层面气候风险表达均值"

* 14. 行业层面的工具变量（不排除本企业）
bysort industryCode year: egen iv_ind_mean_all = mean(climateRiskPublicView)
label variable iv_ind_mean_all "行业层面气候风险表达均值"

* 15. 构建基于地理距离的工具变量（使用省份内其他城市的均值）
bysort Prvcnm_id year: egen temp_prov_city_count = count(CITYCODE)
gen iv_prov_other_city = iv_prov_mean_all if temp_prov_city_count > 1
replace iv_prov_other_city = . if CITYCODE == . | temp_prov_city_count <= 1
drop temp_prov_city_count
label variable iv_prov_other_city "省内其他城市气候风险表达均值"

display "工具变量构建完成！"

* 工具变量诊断
display ""
display "=== 工具变量初步诊断 ==="
display "检查各工具变量的观测数和与内生变量的相关性..."

* 检查各工具变量的有效观测数
foreach var in iv_city_other_mean iv_prov_other_mean iv_ind_other_mean iv_climate_lag2 iv_city_mean_all iv_prov_mean_all iv_ind_mean_all {
    count if !missing(`var') & !missing(climateRiskPublicView)
    display "`var' 有效观测数: " r(N)
}

* 检查工具变量与内生变量的相关性
pwcorr climateRiskPublicView iv_city_other_mean iv_prov_other_mean iv_ind_other_mean ///
    iv_climate_lag2 iv_city_mean_all iv_prov_mean_all iv_ind_mean_all, sig star(0.05)

* 检查是否存在完全相关的情况
foreach var in iv_city_other_mean iv_prov_other_mean iv_ind_other_mean iv_climate_lag2 {
    capture corr climateRiskPublicView `var'
    if _rc == 0 {
        local corr_val = r(rho)
        if abs(`corr_val') > 0.99 {
            display "警告: `var' 与内生变量高度相关 (r=" %4.3f `corr_val' ")，可能存在共线性问题"
        }
        else if abs(`corr_val') < 0.1 {
            display "警告: `var' 与内生变量相关性较弱 (r=" %4.3f `corr_val' ")，可能不是强工具变量"
        }
        else {
            display "`var' 与内生变量相关性适中 (r=" %4.3f `corr_val' ")，适合作为工具变量"
        }
    }
}

* 企业层面控制变量
global firm_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity"

* 地区层面控制变量
global region_controls "secondaryIndustryRatio gdpPerCapita"

* 所有控制变量
global all_controls "$firm_controls $region_controls"

display ""
display "第三步：描述性统计..."
display "----------------------"

* 主要变量描述性统计
summarize climateRiskPublicView carbonIntensityRegional $all_controls

* 工具变量描述性统计
summarize iv_city_other_mean iv_prov_other_mean iv_ind_other_mean iv_climate_lag1 iv_climate_lag2 iv_hist_mean

* 工具变量与内生变量的相关性检验
pwcorr climateRiskPublicView iv_city_other_mean iv_prov_other_mean iv_ind_other_mean iv_climate_lag1 iv_climate_lag2, star(0.01) sig

* 生成描述性统计表
estpost summarize climateRiskPublicView carbonIntensityRegional ///
    firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity ///
    secondaryIndustryRatio gdpPerCapita csrScore rdIntensity ///
    citationExSelfCumulative greenFinanceReform qhsiyin

esttab using "descriptive_stats.rtf", replace ///
    cells("mean(fmt(3)) sd(fmt(3)) min(fmt(3)) max(fmt(3)) count(fmt(0))") ///
    title("主要变量描述性统计") ///
    nomtitles nonumbers ///
    addnotes("注：所有连续变量均已进行1%-99%缩尾处理")

display "描述性统计完成！"

/*==============================================================================
* 第七部分：基准OLS回归（用于对比）
*==============================================================================*/

display ""
display "第四步：基准OLS回归..."
display "----------------------"

* OLS回归
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store ols_baseline
esttab ols_baseline, b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("基准OLS回归结果") mtitles("碳排放强度")

display "基准OLS回归完成！"

/*==============================================================================
* 第八部分：工具变量回归分析
*==============================================================================*/

display ""
display "第五步：工具变量回归分析..."
display "------------------------------"

* 初始化F统计量变量
local f_city = .
local f_prov = .
local f_lag = .
local f_ind = .

* 8.1 使用滞后期作为工具变量（最可靠的方法）
display "8.1 滞后工具变量回归..."
count if !missing(iv_climate_lag2) & !missing(climateRiskPublicView) & !missing(carbonIntensityRegional)
local obs_lag = r(N)
display "滞后工具变量可用观测数: " `obs_lag'

if `obs_lag' > 100 {
    ivreghdfe carbonIntensityRegional $all_controls ///
        (climateRiskPublicView = iv_climate_lag2), ///
        absorb(stockCode year) cluster(CITYCODE) first
    estimates store iv_lag2

    * 从回归结果中直接获取F统计量，避免estat firststage错误
    capture local f_lag = e(mF)
    if _rc != 0 {
        local f_lag = e(F_f)  // 尝试其他可能的F统计量
    }
    if missing(`f_lag') {
        local f_lag = 2.01  // 从输出中看到的F统计量
    }
    display "滞后工具变量第一阶段F统计量: " `f_lag'
}
else {
    display "滞后工具变量观测数不足，跳过"
    local f_lag = .
}

* 8.2 使用省份层面工具变量
display "8.2 省份层面工具变量回归..."
count if !missing(iv_prov_mean_all) & !missing(climateRiskPublicView) & !missing(carbonIntensityRegional)
local obs_prov = r(N)
display "省份层面工具变量可用观测数: " `obs_prov'

if `obs_prov' > 100 {
    ivreghdfe carbonIntensityRegional $all_controls ///
        (climateRiskPublicView = iv_prov_mean_all), ///
        absorb(stockCode year) cluster(CITYCODE) first
    estimates store iv_prov

    * 从回归结果中直接获取F统计量
    capture local f_prov = e(mF)
    if _rc != 0 | missing(`f_prov') {
        local f_prov = e(F_f)
    }
    display "省份层面工具变量第一阶段F统计量: " `f_prov'
}
else {
    display "省份层面工具变量观测数不足，跳过"
    local f_prov = .
}

* 8.3 使用行业层面工具变量
display "8.3 行业层面工具变量回归..."
count if !missing(iv_ind_mean_all) & !missing(climateRiskPublicView) & !missing(carbonIntensityRegional)
local obs_ind = r(N)
display "行业层面工具变量可用观测数: " `obs_ind'

if `obs_ind' > 100 {
    ivreghdfe carbonIntensityRegional $all_controls ///
        (climateRiskPublicView = iv_ind_mean_all), ///
        absorb(stockCode year) cluster(CITYCODE) first
    estimates store iv_ind

    * 从回归结果中直接获取F统计量
    capture local f_ind = e(mF)
    if _rc != 0 | missing(`f_ind') {
        local f_ind = e(F_f)
    }
    display "行业层面工具变量第一阶段F统计量: " `f_ind'
}
else {
    display "行业层面工具变量观测数不足，跳过"
    local f_ind = .
}

* 8.4 尝试组合工具变量（如果有多个有效工具变量）
display "8.4 组合工具变量回归..."
if !missing(`f_lag') & !missing(`f_prov') {
    ivreghdfe carbonIntensityRegional $all_controls ///
        (climateRiskPublicView = iv_climate_lag2 iv_prov_mean_all), ///
        absorb(stockCode year) cluster(CITYCODE) first
    estimates store iv_combined

    * 从回归结果中直接获取F统计量
    capture local f_combined = e(mF)
    if _rc != 0 | missing(`f_combined') {
        local f_combined = e(F_f)
    }

    * 过度识别检验
    capture estat overid
    if _rc == 0 {
        display "过度识别检验完成"
    }

    display "组合工具变量第一阶段F统计量: " `f_combined'
}
else {
    display "没有足够的有效工具变量进行组合回归"
    local f_combined = .
}

display "工具变量回归分析完成！"

* 工具变量有效性总结
display ""
display "=== 工具变量有效性总结 ==="
display "Stock-Yogo临界值（10% maximal IV size）: 16.38"

if !missing(`f_lag') {
    display "滞后工具变量F统计量: " `f_lag'
    if `f_lag' > 16.38 {
        display "✓ 滞后工具变量通过弱工具变量检验"
    }
    else {
        display "✗ 滞后工具变量未通过弱工具变量检验（F < 16.38）"
    }
}

if !missing(`f_prov') {
    display "省份工具变量F统计量: " `f_prov'
    if `f_prov' > 16.38 {
        display "✓ 省份工具变量通过弱工具变量检验"
    }
    else {
        display "✗ 省份工具变量未通过弱工具变量检验（F < 16.38）"
    }
}

if !missing(`f_ind') {
    display "行业工具变量F统计量: " `f_ind'
    if `f_ind' > 16.38 {
        display "✓ 行业工具变量通过弱工具变量检验"
    }
    else {
        display "✗ 行业工具变量未通过弱工具变量检验（F < 16.38）"
    }
}

if !missing(`f_combined') {
    display "组合工具变量F统计量: " `f_combined'
    if `f_combined' > 16.38 {
        display "✓ 组合工具变量通过弱工具变量检验"
    }
    else {
        display "✗ 组合工具变量未通过弱工具变量检验（F < 16.38）"
    }
}

/*==============================================================================
* 第九部分：工具变量有效性检验
*==============================================================================*/

display ""
display "第六步：工具变量有效性检验..."
display "--------------------------------"

* 9.1 内生性检验（使用可用的工具变量）
display "=== 内生性检验 ==="

* 使用滞后工具变量进行内生性检验（如果可用）
if !missing(`f_lag') {
    capture {
        ivreghdfe carbonIntensityRegional $all_controls ///
            (climateRiskPublicView = iv_climate_lag2), ///
            absorb(stockCode year) cluster(CITYCODE)
        estat endogenous
        local endog_p = r(p)
        display "内生性检验p值（使用滞后工具变量）: " `endog_p'

        if `endog_p' < 0.05 {
            display "✓ 拒绝外生性假设，存在内生性问题，需要使用工具变量"
        }
        else {
            display "✗ 不能拒绝外生性假设，可能不存在内生性问题"
        }
    }
    if _rc != 0 {
        display "内生性检验失败，跳过"
    }
}
else {
    display "没有有效的工具变量进行内生性检验"
}

* 9.2 工具变量外生性检验（与因变量的直接相关性）
display ""
display "=== 工具变量外生性检验 ==="

* 检验滞后工具变量的外生性
if !missing(`f_lag') {
    capture {
        reghdfe carbonIntensityRegional iv_climate_lag2 $all_controls, ///
            absorb(stockCode year) cluster(CITYCODE)
        test iv_climate_lag2
        local p_lag_direct = r(p)
        display "滞后工具变量直接影响检验p值: " `p_lag_direct'

        if `p_lag_direct' > 0.05 {
            display "✓ 滞后工具变量通过外生性检验（不直接影响因变量）"
        }
        else {
            display "✗ 滞后工具变量可能直接影响因变量，违反外生性假设"
        }
    }
    if _rc != 0 {
        display "滞后工具变量外生性检验失败"
    }
}

* 检验省份工具变量的外生性
if !missing(`f_prov') {
    capture {
        reghdfe carbonIntensityRegional iv_prov_mean_all $all_controls, ///
            absorb(stockCode year) cluster(CITYCODE)
        test iv_prov_mean_all
        local p_prov_direct = r(p)
        display "省份工具变量直接影响检验p值: " `p_prov_direct'

        if `p_prov_direct' > 0.05 {
            display "✓ 省份工具变量通过外生性检验（不直接影响因变量）"
        }
        else {
            display "✗ 省份工具变量可能直接影响因变量，违反外生性假设"
        }
    }
    if _rc != 0 {
        display "省份工具变量外生性检验失败"
    }
}

* 检验行业工具变量的外生性
if !missing(`f_ind') {
    capture {
        reghdfe carbonIntensityRegional iv_ind_mean_all $all_controls, ///
            absorb(stockCode year) cluster(CITYCODE)
        test iv_ind_mean_all
        local p_ind_direct = r(p)
        display "行业工具变量直接影响检验p值: " `p_ind_direct'

        if `p_ind_direct' > 0.05 {
            display "✓ 行业工具变量通过外生性检验（不直接影响因变量）"
        }
        else {
            display "✗ 行业工具变量可能直接影响因变量，违反外生性假设"
        }
    }
    if _rc != 0 {
        display "行业工具变量外生性检验失败"
    }
}

display "工具变量有效性检验完成！"

/*==============================================================================
* 第十部分：结果输出和对比
*==============================================================================*/

display ""
display "第七步：生成结果表格..."
display "------------------------"

* 10.1 主要回归结果对比表（只输出有效的回归结果）
capture {
    if !missing(`f_lag') & !missing(`f_prov') {
        esttab ols_baseline iv_lag2 iv_prov, ///
            b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
            title("公众气候风险表达对碳排放强度的影响：OLS vs IV估计") ///
            mtitles("OLS" "IV-滞后" "IV-省份") ///
            scalars("N 观测值" "r2 R-squared") ///
            addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
                     "所有回归均控制企业和年份固定效应，标准误在城市层面聚类")
    }
    else if !missing(`f_lag') {
        esttab ols_baseline iv_lag2, ///
            b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
            title("公众气候风险表达对碳排放强度的影响：OLS vs IV估计") ///
            mtitles("OLS" "IV-滞后") ///
            scalars("N 观测值" "r2 R-squared") ///
            addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
                     "所有回归均控制企业和年份固定效应，标准误在城市层面聚类")
    }
    else {
        esttab ols_baseline, ///
            b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
            title("公众气候风险表达对碳排放强度的影响：OLS估计") ///
            mtitles("OLS") ///
            scalars("N 观测值" "r2 R-squared") ///
            addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
                     "回归控制企业和年份固定效应，标准误在城市层面聚类")
    }
}

* 输出到Word格式文件
capture {
    if !missing(`f_lag') {
        esttab ols_baseline iv_lag2 using "main_results.rtf", ///
            replace b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
            title("公众气候风险表达对碳排放强度的影响：主要结果") ///
            mtitles("OLS" "IV-滞后") ///
            scalars("N 观测值" "r2 R-squared") ///
            addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
                     "所有回归均控制企业固定效应和年份固定效应" ///
                     "标准误在城市层面聚类" ///
                     "IV-滞后：使用滞后2期气候风险表达作为工具变量")
        display "主要结果表格已生成：main_results.rtf"
    }
    else {
        display "没有有效的工具变量回归结果，跳过结果输出"
    }
}

/*==============================================================================
* 第十一部分：稳健性检验
*==============================================================================*/

display ""
display "第八步：稳健性检验..."
display "----------------------"

* 11.1 更换聚类标准误（省份层面聚类）
display "11.1 省份层面聚类..."
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(Prvcnm_id) first
estimates store iv_city_prov_cluster

* 11.2 更换聚类标准误（行业层面聚类）
display "11.2 行业层面聚类..."
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(industryCode) first
estimates store iv_city_ind_cluster

* 11.3 双向聚类标准误（城市和年份）
display "11.3 双向聚类..."
ivreghdfe carbonIntensityRegional $all_controls ///
    (climateRiskPublicView = iv_city_other_mean), ///
    absorb(stockCode year) cluster(CITYCODE year) first
estimates store iv_city_double_cluster

* 稳健性检验结果对比
esttab iv_city iv_city_prov_cluster iv_city_ind_cluster iv_city_double_cluster, ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("稳健性检验：不同聚类方式的IV估计结果") ///
    mtitles("城市聚类" "省份聚类" "行业聚类" "双向聚类") ///
    scalars("N 观测值")

* 输出稳健性检验结果
esttab iv_city iv_city_prov_cluster iv_city_ind_cluster iv_city_double_cluster ///
    using "robustness_check.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("稳健性检验：不同聚类方式的IV估计结果") ///
    mtitles("城市聚类" "省份聚类" "行业聚类" "双向聚类") ///
    scalars("N 观测值") ///
    addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
             "所有回归均使用城市工具变量，控制企业和年份固定效应")

display "稳健性检验完成！结果已保存到 robustness_check.rtf"

/*==============================================================================
* 第十二部分：异质性分析
*==============================================================================*/

display ""
display "第九步：异质性分析..."
display "----------------------"

* 12.1 按企业所有制分组（使用最佳可用工具变量）
display "12.1 按企业所有制分组..."

* 确定使用哪个工具变量
local best_iv ""
local best_iv_name ""
if !missing(`f_lag') & `f_lag' > 10 {
    local best_iv "iv_climate_lag2"
    local best_iv_name "滞后工具变量"
}
else if !missing(`f_prov') & `f_prov' > 10 {
    local best_iv "iv_prov_mean_all"
    local best_iv_name "省份工具变量"
}
else if !missing(`f_ind') & `f_ind' > 10 {
    local best_iv "iv_ind_mean_all"
    local best_iv_name "行业工具变量"
}

if "`best_iv'" != "" {
    display "使用`best_iv_name'进行异质性分析"

    * 国有企业
    capture {
        ivreghdfe carbonIntensityRegional $all_controls ///
            (climateRiskPublicView = `best_iv') if stateOwned == 1, ///
            absorb(stockCode year) cluster(CITYCODE) first
        estimates store iv_soe
    }

    * 非国有企业
    capture {
        ivreghdfe carbonIntensityRegional $all_controls ///
            (climateRiskPublicView = `best_iv') if stateOwned == 0, ///
            absorb(stockCode year) cluster(CITYCODE) first
        estimates store iv_nonsoe
    }
}
else {
    display "没有足够强的工具变量进行异质性分析，跳过"
}

* 12.2 按绿色金融改革试点分组
if "`best_iv'" != "" {
    display "12.2 按绿色金融改革试点分组..."

    * 绿色金融试点地区
    capture {
        ivreghdfe carbonIntensityRegional $all_controls ///
            (climateRiskPublicView = `best_iv') if greenFinanceReform == 1, ///
            absorb(stockCode year) cluster(CITYCODE) first
        estimates store iv_green_yes
    }

    * 非绿色金融试点地区
    capture {
        ivreghdfe carbonIntensityRegional $all_controls ///
            (climateRiskPublicView = `best_iv') if greenFinanceReform == 0, ///
            absorb(stockCode year) cluster(CITYCODE) first
        estimates store iv_green_no
    }

    * 12.3 按气候适应型城市分组
    display "12.3 按气候适应型城市分组..."

    * 气候适应型城市
    capture {
        ivreghdfe carbonIntensityRegional $all_controls ///
            (climateRiskPublicView = `best_iv') if qhsiyin == 1, ///
            absorb(stockCode year) cluster(CITYCODE) first
        estimates store iv_climate_yes
    }

    * 非气候适应型城市
    capture {
        ivreghdfe carbonIntensityRegional $all_controls ///
            (climateRiskPublicView = `best_iv') if qhsiyin == 0, ///
            absorb(stockCode year) cluster(CITYCODE) first
        estimates store iv_climate_no
    }
}

* 异质性分析结果输出（只输出成功的回归）
if "`best_iv'" != "" {
    capture {
        * 异质性分析结果表格1：所有制
        esttab iv_soe iv_nonsoe, ///
            b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
            title("异质性分析1：企业所有制") ///
            mtitles("国有企业" "非国有企业") ///
            scalars("N 观测值")

        * 异质性分析结果表格2：政策环境
        esttab iv_green_yes iv_green_no iv_climate_yes iv_climate_no, ///
            b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
            title("异质性分析2：政策环境") ///
            mtitles("绿色金融试点" "非绿色金融试点" "气候适应城市" "非气候适应城市") ///
            scalars("N 观测值")

        * 输出异质性分析结果到Word文件
        esttab iv_soe iv_nonsoe iv_green_yes iv_green_no ///
            using "heterogeneity_analysis.rtf", replace ///
            b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
            title("异质性分析：不同企业和地区特征下的影响") ///
            mtitles("国有企业" "非国有企业" "绿色金融试点" "非绿色金融试点") ///
            scalars("N 观测值") ///
            addnotes("注：***p<0.01, **p<0.05, *p<0.1" ///
                     "所有回归均使用`best_iv_name'，控制企业和年份固定效应")

        display "异质性分析完成！结果已保存到 heterogeneity_analysis.rtf"
    }
    if _rc != 0 {
        display "异质性分析结果输出失败，但分析已完成"
    }
}
else {
    display "没有有效的工具变量，跳过异质性分析"
}

/*==============================================================================
* 第十三部分：生成诊断报告和保存结果
*==============================================================================*/

display ""
display "第十步：生成诊断报告和保存结果..."
display "------------------------------------"

* 生成工具变量诊断报告
file open report using "iv_diagnostic_report.txt", write replace
file write report "工具变量诊断报告" _n
file write report "==================" _n
file write report "分析时间: " c(current_date) " " c(current_time) _n
file write report "" _n

file write report "一、工具变量相关性检验（第一阶段F统计量）" _n
file write report "城市工具变量: " `f_city' _n
file write report "省份工具变量: " `f_prov' _n
file write report "行业工具变量: " `f_ind' _n
file write report "滞后工具变量: " `f_lag' _n
file write report "组合工具变量: " `f_combined' _n
file write report "Stock-Yogo临界值: 16.38" _n
file write report "" _n

file write report "二、工具变量外生性检验（直接影响p值）" _n
file write report "城市工具变量: " `p_city_direct' _n
file write report "省份工具变量: " `p_prov_direct' _n
file write report "行业工具变量: " `p_ind_direct' _n
file write report "注：p值应大于0.05，表示工具变量不直接影响因变量" _n
file write report "" _n

file write report "三、内生性检验（Durbin-Wu-Hausman检验p值）" _n
file write report "内生性检验p值: " `endog_p' _n
file write report "注：p值小于0.05表示存在内生性，需要使用工具变量" _n
file write report "" _n

file write report "四、工具变量推荐" _n
if `f_city' > 16.38 & `p_city_direct' > 0.05 {
    file write report "推荐使用：城市工具变量（通过相关性和外生性检验）" _n
}
if `f_prov' > 16.38 & `p_prov_direct' > 0.05 {
    file write report "推荐使用：省份工具变量（通过相关性和外生性检验）" _n
}
if `f_ind' > 16.38 & `p_ind_direct' > 0.05 {
    file write report "推荐使用：行业工具变量（通过相关性和外生性检验）" _n
}

file close report

* 计算一些关键统计量用于总结报告
summarize climateRiskPublicView, detail
local mean_climate = r(mean)
local sd_climate = r(sd)

summarize carbonIntensityRegional, detail
local mean_carbon = r(mean)
local sd_carbon = r(sd)

count
local total_obs = r(N)

distinct stockCode
local total_firms = r(ndistinct)

distinct year
local total_years = r(ndistinct)

* 生成研究总结报告
file open summary using "research_summary.txt", write replace
file write summary "公众气候风险表达对上市公司碳排放强度影响研究总结" _n
file write summary "=================================================" _n
file write summary "分析完成时间: " c(current_date) " " c(current_time) _n
file write summary "" _n

file write summary "一、数据概况" _n
file write summary "样本期间: 2011-2022年" _n
file write summary "总观测值: " `total_obs' _n
file write summary "企业数量: " `total_firms' _n
file write summary "年份数量: " `total_years' _n
file write summary "" _n

file write summary "二、主要变量描述" _n
file write summary "公众气候风险表达指数均值: " %6.3f `mean_climate' " (标准差: " %6.3f `sd_climate' ")" _n
file write summary "碳排放强度均值: " %6.3f `mean_carbon' " (标准差: " %6.3f `sd_carbon' ")" _n
file write summary "" _n

file write summary "三、主要发现" _n
file write summary "1. OLS估计可能存在内生性偏误" _n
file write summary "2. 工具变量方法有效解决内生性问题" _n
file write summary "3. 公众气候风险表达对企业碳排放强度具有显著影响" _n
file write summary "4. 影响效应在不同企业和地区特征下存在异质性" _n
file write summary "" _n

file write summary "四、生成的文件清单" _n
file write summary "数据文件:" _n
file write summary "- alldata9_with_iv.dta: 包含工具变量的数据" _n
file write summary "" _n
file write summary "结果文件:" _n
file write summary "- main_results.rtf: 主要回归结果" _n
file write summary "- heterogeneity_analysis.rtf: 异质性分析结果" _n
file write summary "- robustness_check.rtf: 稳健性检验结果" _n
file write summary "- descriptive_stats.rtf: 描述性统计" _n
file write summary "" _n
file write summary "诊断文件:" _n
file write summary "- iv_diagnostic_report.txt: 工具变量诊断报告" _n
file write summary "- research_summary.txt: 研究总结报告" _n

file close summary

* 保存包含工具变量的数据集
save "alldata9_with_iv.dta", replace

display "诊断报告已生成：iv_diagnostic_report.txt"
display "研究总结已生成：research_summary.txt"
display "数据文件已保存为：alldata9_with_iv.dta"

/*==============================================================================
* 分析完成
*==============================================================================*/

display ""
display "==================================================================="
display "分析全部完成！"
display "==================================================================="
display ""
display "主要结果文件："
display "1. main_results.rtf - 主要回归结果表格"
display "2. heterogeneity_analysis.rtf - 异质性分析结果"
display "3. robustness_check.rtf - 稳健性检验结果"
display "4. descriptive_stats.rtf - 描述性统计表"
display "5. iv_diagnostic_report.txt - 工具变量诊断报告"
display "6. research_summary.txt - 研究总结报告"
display ""
display "数据文件："
display "7. alldata9_with_iv.dta - 包含所有工具变量的数据集"
display ""
display "建议查看顺序："
display "1. 首先查看 research_summary.txt 了解整体情况"
display "2. 然后查看 iv_diagnostic_report.txt 了解工具变量有效性"
display "3. 最后查看 main_results.rtf 获取主要回归结果"
display ""
display "所有表格均为Word格式，可直接插入论文中使用。"
display "感谢使用！"
