---

oneclick 命令帮助文档
-----------------

标题

    oneclick —— 帮助您快速筛选控制变量，使解释变量保持在特定显著性水平。

语法

    oneclick y controls, method(regression) pvalue(p-value) fixvar(x and
             other FE) [ options(extra-options) zvalue threshold(int)
             saveplace(filename) best full ]

    如果您使用reghdfe命令，请尽可能将固定效应放入absorb选项中以加快计算速度。

    如果您不习惯oneclick的新版本，我在此版本中保留了oneclick5。
    您可以使用oneclick5实现之前版本的功能。

描述

    通过输入您的控制变量，oneclick命令帮助您选择控制变量的所有真子集，
    并依次将它们添加到回归中，最后只列出使您满意的显著性水平的组合。
    我基于位图算法从头构建了子集筛选方法，比元组更快。

要求

    varlist 指定因变量和待筛选的控制变量。如果您输入oneclick a b c，
        则a是您的因变量，b和c是等待筛选的控制变量。

    method(regression) 指定您要使用的估计器。

    pvalue(p-value) 指定显著性水平。

    fixvar(varlist) 指定您希望在回归中固定的自变量和其他变量。
        如果您输入a b c，则a是您的自变量，b和c是必须包含的控制变量。

    options(extra-options) 指定回归中的附加选项。
        如果您使用reghdfe，可以添加o(absorb(#))。

    zvalue 指定回归是否通过z值判断。如果您使用logit、probit或xtreg（默认）等回归，
        必须添加z选项。

    threshold 指定您需要在结果中保留的可选控制变量的最小数量。

    saveplace 指定您需要将结果文件重命名为什么。

    best 让oneclick自动为您选择最佳回归结果。

    full 让oneclick为您填充结果，使其更易读。

结果

    运行oneclick后，您将在当前工作目录中看到一个名为subset的dta文件。
    其中，变量subset表示可以使解释变量显著的控制变量，
    变量positive取值为1表示正向显著，取值为0表示负向显著。

代码示例

    *- 从mpg和rep78中选择组合，使weight在regress中达到10%的显著性水平。

    - sysuse auto.dta, clear
    - oneclick price mpg rep78, fix(weight) p(0.1) m(reg)

    *- 从mpg和rep78中选择组合，使weight在带固定效应的regress中达到10%的显著性水平。
    **- 个体固定效应：foreign。

    - sysuse auto.dta, clear
    - oneclick price mpg rep78, fix(weight i.foreign) p(0.1) m(reg)

    *- 从mpg和rep78中选择组合，使weight在带固定效应和稳健标准误的regress中达到10%的显著性水平。
    **- 个体固定效应：foreign。

    - sysuse auto.dta, clear
    - oneclick price mpg rep78, fix(weight i.foreign) p(0.1) m(reg)
    o(vce(robust))

    *- 从mpg和rep78中选择组合，使weight在带固定效应和聚类标准误的regress中达到10%的显著性水平。
    **- 个体固定效应：foreign。

    - sysuse auto.dta, clear
    - oneclick price mpg rep78, fix(weight i.foreign) p(0.1) m(reg)
    o(vce(cluster foreign))

    *- 从mpg和rep78中选择组合，使weight在带固定效应的xtreg中达到10%的显著性水平。
    **- 个体固定效应：foreign。
    **- 时间固定效应：year。

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - xtset foreign year
    - oneclick price mpg rep78, fix(weight i.year) p(0.1) m(xtreg) o(fe)

    *- 从mpg和rep78中选择组合，使weight在带固定效应和稳健标准误的xtreg中达到10%的显著性水平。
    **- 个体固定效应：foreign。
    **- 时间固定效应：year。

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - xtset foreign year
    - oneclick price mpg rep78, fix(weight i.year) p(0.1) m(xtreg) o(fe
    vce(robust))

    *- 从mpg和rep78中选择组合，使weight在带固定效应和聚类标准误的xtreg中达到10%的显著性水平。
    **- 个体固定效应：foreign。
    **- 时间固定效应：year。

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - xtset foreign year
    - oneclick price mpg rep78, fix(weight i.year) p(0.1) m(xtreg) o(fe
    vce(cluster foreign))

    *- 从mpg和rep78中选择组合，使weight在带固定效应的reghdfe中达到10%的显著性水平。
    **- 个体固定效应：foreign。
    **- 时间固定效应：year。

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - oneclick price mpg rep78, fix(weight) p(0.1) m(reghdfe)
    o(absorb(foreign year))

    *- 从mpg和rep78中选择组合，使weight在带固定效应和稳健标准误的reghdfe中达到10%的显著性水平。
    **- 个体固定效应：foreign。
    **- 时间固定效应：year。

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - oneclick price mpg rep78, fix(weight) p(0.1) m(reghdfe)
    o(absorb(foreign year) vce(robust))

    *- 从mpg和rep78中选择组合，使weight在带固定效应和聚类标准误的reghdfe中达到10%的显著性水平。
    **- 个体固定效应：foreign。
    **- 时间固定效应：year。

    - sysuse auto.dta, clear
    - bys foreign: gen year = _n
    - oneclick price mpg rep78, fix(weight) p(0.1) m(reghdfe)
    o(absorb(foreign year) vce(cluster foreign))

    *- 如果您使用基于z值而非t值判断显著性的回归方法（如logit或probit），
    不要忘记在oneclick末尾添加z选项。

    - sysuse auto.dta, clear
    - oneclick foreign mpg rep78, fix(weight) p(0.1) m(logit) z
    - oneclick foreign mpg rep78, fix(weight) p(0.1) m(probit) z

视频教程

    在此链接观看最新视频：oneclick视频教程

作者

    基本信息
    姓名：左祥太（Shutter Zor）
    单位：厦门大学会计系
    电子邮箱：<EMAIL>

    其他信息
    博客：博客链接
    哔哩哔哩：拿铁一定要加冰
    微信公众号：OneStata

我编写的其他命令

    onetext（如已安装）           ssc install onetext（安装）
    econsig（如已安装）           ssc install econsig（安装）
    wordcloud（如已安装）         ssc install wordcloud（安装）

致谢

    感谢连玉君教授（arlionn）的编程语法指导和Christopher F. Baum教授的仔细错误检查。
    感谢哔哩哔哩用户（导导们）对本项目初期构建的建议。
