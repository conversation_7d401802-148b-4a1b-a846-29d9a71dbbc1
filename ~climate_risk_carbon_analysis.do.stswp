/*==============================================================================
* 文件名称: climate_risk_carbon_analysis.do
* 研究主题: 中国的公众气候风险表达对上市公司碳排放强度的影响
* 作者: [作者姓名]
* 创建日期: 2025年
* 数据时间: 2011-2022
* 数据来源: /Users/<USER>/Desktop/clitansz/alldata9.dta
*==============================================================================*/

/*------------------------------------------------------------------------------
* 第一部分：环境设置和数据准备
*------------------------------------------------------------------------------*/

// 清空内存和设置环境
clear all
set more off
set matsize 10000
capture log close

// 设置工作目录
cd "/Users/<USER>/Desktop/clitansz"

// 开始记录日志
log using "climate_risk_analysis.log", replace

// 安装必要的命令包（如果尚未安装）
ssc install reghdfe, replace
ssc install estout, replace
ssc install winsor2, replace
ssc install outreg2, replace
ssc install asdoc, replace

/*------------------------------------------------------------------------------
* 第二部分：宏定义和变量设置
*------------------------------------------------------------------------------*/

// 定义控制变量宏（使用数据中的实际变量名）
global firm_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity"
global region_controls "secondaryIndustryRatio gdpPerCapita"
global all_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity secondaryIndustryRatio gdpPerCapita"

/*------------------------------------------------------------------------------
* 第三部分：数据载入和预处理
*------------------------------------------------------------------------------*/

// 载入数据
use "alldata9.dta", clear

// 数据基本信息
describe
summarize

// 设置面板数据结构
xtset stockCode year

// 删除关键变量缺失的观测值
drop if missing(carbonIntensityRegional) | missing(climateRiskPublicView)

// 对连续变量进行缩尾处理（1%和99%分位数）
foreach var of varlist carbonIntensityRegional climateRiskPublicView $all_controls citationExSelfCumulative rdIntensity csrScore {
    winsor2 `var', replace cuts(1 99)
}

// 生成交互项
gen climate_green_finance = climateRiskPublicView * greenFinanceReform
gen climate_adaptive_city = climateRiskPublicView * qhsiyin

// 标记样本（逐个检查变量以避免宏展开问题）
gen sample_main = 1 if !missing(carbonIntensityRegional) & !missing(climateRiskPublicView) & ///
    !missing(firmSize) & !missing(firmAge) & !missing(leverage) & !missing(ownershipBalance) & ///
    !missing(stateOwned) & !missing(tobinsQ1) & !missing(returnOnEquity) & ///
    !missing(secondaryIndustryRatio) & !missing(gdpPerCapita)

/*------------------------------------------------------------------------------
* 第四部分：描述性统计分析
*------------------------------------------------------------------------------*/

// 主要变量描述性统计
asdoc summarize carbonIntensityRegional climateRiskPublicView csrScore citationExSelfCumulative rdIntensity ///
    firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity ///
    secondaryIndustryRatio gdpPerCapita if sample_main==1, ///
    stat(N mean sd min max) dec(3) title(主要变量描述性统计) save(descriptive_stats.doc) replace

// 按年份的描述性统计
preserve
collapse (mean) mean_carbon=carbonIntensityRegional (mean) mean_climate=climateRiskPublicView ///
    (count) N=carbonIntensityRegional, by(year)
list year mean_carbon mean_climate N
restore

// 按行业的描述性统计（如果有行业变量）
preserve
capture {
    collapse (mean) mean_carbon=carbonIntensityRegional (mean) mean_climate=climateRiskPublicView ///
        (count) N=carbonIntensityRegional, by(industryName)
    gsort -mean_carbon
    list industryName mean_carbon mean_climate N in 1/10
}
restore

/*------------------------------------------------------------------------------
* 第五部分：相关性分析
*------------------------------------------------------------------------------*/

// 生成相关系数矩阵
pwcorr carbonIntensityRegional climateRiskPublicView csrScore citationExSelfCumulative rdIntensity $all_controls if sample_main==1, sig star(.01) star5(.05) star10(.10)

// 导出相关系数矩阵到Word
asdoc pwcorr carbonIntensityRegional climateRiskPublicView csrScore citationExSelfCumulative rdIntensity $all_controls if sample_main==1, ///
    sig setstars(*@0.10, **@0.05, ***@0.01) title(变量相关系数矩阵) save(correlation_matrix.doc) replace

/*------------------------------------------------------------------------------
* 第六部分：基准回归分析
*------------------------------------------------------------------------------*/

// 模型1：仅包含核心解释变量
reghdfe carbonIntensityRegional climateRiskPublicView, absorb(stockCode year) cluster(CITYCODE)
estimates store model1

// 模型2：加入企业控制变量
reghdfe carbonIntensityRegional climateRiskPublicView $firm_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store model2

// 模型3：加入地区控制变量（完整模型）
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store model3

// 导出基准回归结果
esttab model1 model2 model3 using "baseline_regression.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("基准回归结果：公众气候风险表达对碳排放强度的影响") ///
    mtitles("模型1" "模型2" "模型3") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

/*------------------------------------------------------------------------------
* 第七部分：机制分析
*------------------------------------------------------------------------------*/

// 机制1：企业社会责任中介效应
// 第一步：X对M的回归
reghdfe csrScore climateRiskPublicView $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store mechanism1_step1

// 第二步：X和M对Y的回归
reghdfe carbonIntensityRegional climateRiskPublicView csrScore $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store mechanism1_step2

// 机制2：绿色创新质量中介效应
// 第一步：X对M的回归
reghdfe citationExSelfCumulative climateRiskPublicView $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store mechanism2_step1

// 第二步：X和M对Y的回归
reghdfe carbonIntensityRegional climateRiskPublicView citationExSelfCumulative $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store mechanism2_step2

// 机制3：研发投入中介效应
// 第一步：X对M的回归
reghdfe rdIntensity climateRiskPublicView $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store mechanism3_step1

// 第二步：X和M对Y的回归
reghdfe carbonIntensityRegional climateRiskPublicView rdIntensity $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store mechanism3_step2

// 导出机制分析结果（第一步：X对M的回归）
esttab mechanism1_step1 mechanism2_step1 mechanism3_step1 using "mechanism_analysis_step1.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("机制分析第一步：解释变量对中介变量的影响") ///
    mtitles("企业社会责任" "绿色创新质量" "研发投入强度") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：第一步检验解释变量（公众气候风险表达）对中介变量的影响；" ///
             "企业社会责任(csrScore)、绿色创新质量(citationExSelfCumulative)、研发投入强度(rdIntensity)；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

// 导出机制分析结果（第二步：X和M对Y的回归）
esttab mechanism1_step2 mechanism2_step2 mechanism3_step2 using "mechanism_analysis_step2.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("机制分析第二步：解释变量和中介变量对被解释变量的影响") ///
    mtitles("企业社会责任机制" "绿色创新质量机制" "研发投入强度机制") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：第二步检验解释变量和中介变量对被解释变量（碳排放强度）的影响；" ///
             "如果解释变量系数显著且中介变量系数显著，则存在中介效应；" ///
             "如果解释变量系数不显著但中介变量系数显著，则为完全中介；" ///
             "如果解释变量系数显著且中介变量系数显著，则为部分中介；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

/*------------------------------------------------------------------------------
* 第八部分：调节效应分析（异质性分析）
*------------------------------------------------------------------------------*/

// 调节效应1：绿色金融改革创新试验区的调节作用
reghdfe carbonIntensityRegional climateRiskPublicView greenFinanceReform climate_green_finance $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store moderator1

// 调节效应2：气候适应型城市的调节作用
reghdfe carbonIntensityRegional climateRiskPublicView qhsiyin climate_adaptive_city $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store moderator2

// 同时包含两个调节变量
reghdfe carbonIntensityRegional climateRiskPublicView greenFinanceReform qhsiyin ///
    climate_green_finance climate_adaptive_city $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store moderator3

// 导出调节效应结果
esttab moderator1 moderator2 moderator3 using "moderator_analysis.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("调节效应分析：异质性检验") ///
    mtitles("绿色金融调节" "适应城市调节" "双重调节") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

/*------------------------------------------------------------------------------
* 第九部分：稳健性检验
*------------------------------------------------------------------------------*/

// 稳健性检验1：更换Tobin Q的度量方式（四种不同的Tobin Q）
// 基准模型使用tobinsQ1
reghdfe carbonIntensityRegional climateRiskPublicView firmSize firmAge leverage ownershipBalance ///
    stateOwned tobinsQ1 returnOnEquity $region_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store robust_tobinq1

// 使用tobinsQ2
reghdfe carbonIntensityRegional climateRiskPublicView firmSize firmAge leverage ownershipBalance ///
    stateOwned tobinsQ2 returnOnEquity $region_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store robust_tobinq2

// 使用tobinsQ3
reghdfe carbonIntensityRegional climateRiskPublicView firmSize firmAge leverage ownershipBalance ///
    stateOwned tobinsQ3 returnOnEquity $region_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store robust_tobinq3

// 使用tobinsQ4
reghdfe carbonIntensityRegional climateRiskPublicView firmSize firmAge leverage ownershipBalance ///
    stateOwned tobinsQ4 returnOnEquity $region_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store robust_tobinq4

// 稳健性检验2：滞后一期的解释变量
gen L_climateRiskPublicView = L.climateRiskPublicView
reghdfe carbonIntensityRegional L_climateRiskPublicView $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store robust2

// 稳健性检验3：更换研发投入机制变量的度量方式
// 3.1 使用研发人员数量
reghdfe carbonIntensityRegional climateRiskPublicView rdStaff $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store robust_rd1

// 3.2 使用研发投入金额
reghdfe carbonIntensityRegional climateRiskPublicView rdExpenditure $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store robust_rd2

// 3.3 使用研发投入费用化金额
reghdfe carbonIntensityRegional climateRiskPublicView rdExpensed $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store robust_rd3

// 3.4 使用研发投入资本化金额
reghdfe carbonIntensityRegional climateRiskPublicView rdCapitalized $all_controls, absorb(stockCode year) cluster(CITYCODE)
estimates store robust_rd4

// 稳健性检验4：更换创新质量机制变量的度量方式
// 4.1 使用企业对应年份时的总的被引用数
reghdfe carbonIntensityRegional climateRiskPublicView citationTotalCumulative $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store robust_innov1

// 4.2 生成滞后一期的企业对应年份时的总的被引用数
gen L_citationTotalCumulative = L.citationTotalCumulative
reghdfe carbonIntensityRegional climateRiskPublicView L_citationTotalCumulative $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store robust_innov2

// 4.3 生成滞后一期的企业绿色专利被引用数（剔除自引）
gen L_citationExSelfCumulative = L.citationExSelfCumulative
reghdfe carbonIntensityRegional climateRiskPublicView L_citationExSelfCumulative $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store robust_innov3

// 导出Tobin Q稳健性检验结果（四种度量方式在同一表格）
esttab robust_tobinq1 robust_tobinq2 robust_tobinq3 robust_tobinq4 using "robustness_tobinq.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("稳健性检验：四种Tobin Q度量方式") ///
    mtitles("TobinQ1" "TobinQ2" "TobinQ3" "TobinQ4") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：TobinQ1=市值A/资产总计；TobinQ2=市值A/(资产总计-无形资产-商誉)；" ///
             "TobinQ3=市值B/资产总计；TobinQ4=市值B/(资产总计-无形资产-商誉)；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

// 导出研发投入机制变量稳健性检验结果
esttab robust_rd1 robust_rd2 robust_rd3 robust_rd4 using "robustness_rd_mechanism.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("稳健性检验：研发投入机制变量的不同度量方式") ///
    mtitles("研发人员数量" "研发投入金额" "研发费用化金额" "研发资本化金额") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：研发人员数量(rdStaff)、研发投入金额(rdExpenditure)、" ///
             "研发费用化金额(rdExpensed)、研发资本化金额(rdCapitalized)；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

// 导出创新质量机制变量稳健性检验结果
esttab robust_innov1 robust_innov2 robust_innov3 using "robustness_innovation_mechanism.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("稳健性检验：创新质量机制变量的不同度量方式") ///
    mtitles("累计被引用数" "滞后累计被引用数" "滞后剔除自引被引用数") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：累计被引用数(citationTotalCumulative)、" ///
             "滞后一期累计被引用数(L.citationTotalCumulative)、" ///
             "滞后一期剔除自引被引用数(L.citationExSelfCumulative)；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

// 导出滞后解释变量稳健性检验结果
esttab robust2 using "robustness_lagged_variable.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("稳健性检验：滞后解释变量") ///
    mtitles("滞后气候风险表达") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：使用滞后一期的公众气候风险表达指数作为解释变量；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

/*------------------------------------------------------------------------------
* 第九部分补充：高级稳健性检验
*------------------------------------------------------------------------------*/

// 稳健性检验5：PSM倾向得分匹配
// 安装PSM相关命令（如果需要）
capture ssc install psmatch2

// 生成处理组虚拟变量（基于气候风险表达指数的中位数）
summarize climateRiskPublicView, detail
local median_climate = r(p50)
gen high_climate_risk = (climateRiskPublicView > `median_climate')

// 估计倾向得分
logit high_climate_risk $all_controls, cluster(CITYCODE)
predict pscore, pr

// 进行1:1最近邻匹配
psmatch2 high_climate_risk, pscore(pscore) neighbor(1) common caliper(0.01)

// 检查匹配质量
pstest $all_controls, both graph

// 在匹配样本上进行回归
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if _support==1 [weight=_weight], ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store psm_result

// 稳健性检验6：安慰剂检验（Monte Carlo模拟）
// 生成随机的"伪"气候风险表达变量
set seed 12345
gen placebo_climate = .
local iterations = 500

// 存储安慰剂检验结果
matrix placebo_results = J(`iterations', 3, .)
matrix colnames placebo_results = coef se pvalue

display "开始安慰剂检验（Monte Carlo模拟），共`iterations'次迭代..."

forvalues i = 1/`iterations' {
    // 生成随机排列的气候风险表达变量
    quietly {
        replace placebo_climate = .
        bysort year: gen random_order = runiform()
        bysort year (random_order): replace placebo_climate = climateRiskPublicView[_n]
        drop random_order

        // 运行回归
        capture reghdfe carbonIntensityRegional placebo_climate $all_controls, ///
            absorb(stockCode year) cluster(CITYCODE)

        if _rc == 0 {
            matrix placebo_results[`i', 1] = _b[placebo_climate]
            matrix placebo_results[`i', 2] = _se[placebo_climate]
            matrix placebo_results[`i', 3] = 2*ttail(e(df_r), abs(_b[placebo_climate]/_se[placebo_climate]))
        }
    }

    // 显示进度
    if mod(`i', 100) == 0 {
        display "已完成 `i'/`iterations' 次迭代"
    }
}

// 分析安慰剂检验结果
preserve
clear
svmat placebo_results, names(col)
drop if missing(coef)

// 计算统计量
summarize coef, detail
local mean_placebo = r(mean)
local sd_placebo = r(sd)
count if pvalue < 0.05
local sig_count = r(N)
local total_count = _N
local sig_rate = `sig_count' / `total_count'

display "安慰剂检验结果："
display "平均系数: " `mean_placebo'
display "系数标准差: " `sd_placebo'
display "显著率 (p<0.05): " `sig_rate'

// 绘制安慰剂检验结果分布图
histogram coef, normal ///
    xtitle("安慰剂检验系数") ytitle("频率") ///
    title("安慰剂检验系数分布图") ///
    note("红线表示真实系数值") ///
    scheme(s1color)
graph export "placebo_test_distribution.png", replace width(800) height(600)

restore

// 稳健性检验7：动态面板GMM估计
// 安装GMM相关命令（如果需要）
capture ssc install xtabond2

// 生成滞后被解释变量
gen L_carbonIntensity = L.carbonIntensityRegional

// System GMM估计（Blundell-Bond）
xtabond2 carbonIntensityRegional L_carbonIntensity climateRiskPublicView $all_controls, ///
    gmm(L_carbonIntensity, lag(2 4)) ///
    iv(climateRiskPublicView $all_controls) ///
    twostep robust small
estimates store gmm_system

// Difference GMM估计（Arellano-Bond）
xtabond2 carbonIntensityRegional L_carbonIntensity climateRiskPublicView $all_controls, ///
    gmm(L_carbonIntensity, lag(2 4)) ///
    iv(climateRiskPublicView $all_controls) ///
    twostep robust small nodiffsargan
estimates store gmm_diff

// 导出高级稳健性检验结果
esttab psm_result gmm_system gmm_diff using "robustness_advanced.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("高级稳健性检验") ///
    mtitles("PSM匹配" "System GMM" "Difference GMM") ///
    scalars("N 观测值数" "r2_a 调整R方" "hansen Hansen检验p值" "ar2p AR(2)检验p值") ///
    addnotes("注：PSM使用倾向得分匹配；GMM估计控制内生性和动态性；" ///
             "Hansen检验p值>0.1表示工具变量有效；AR(2)检验p值>0.1表示无序列相关；" ///
             "括号内为稳健标准误；*、**、***分别表示在10%、5%、1%水平上显著")

// 稳健性检验8：逐步加入更多控制变量
// 定义额外的控制变量组
global governance_controls "boardSize indDirectorRatio dualityRole bigFourAudit standardAuditOpinion"
global ownership_controls "institutionalOwnership1 managerialOwnership controllingShareholderOccupancy largestShareHolding"
global performance_controls "returnOnAssets assetTurnover revenueGrowth cashFlowOps1 managementExpenseRatio"
global market_controls "bookToMarket1 listingAge"

// 对额外控制变量进行缩尾处理
foreach var of varlist $governance_controls $ownership_controls $performance_controls $market_controls {
    capture winsor2 `var', replace cuts(1 99)
}

// 模型1：基准控制变量（已有的）
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store controls_baseline

// 模型2：加入公司治理控制变量
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls $governance_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store controls_governance

// 模型3：加入股权结构控制变量
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls $governance_controls $ownership_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store controls_ownership

// 模型4：加入经营绩效控制变量
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls $governance_controls $ownership_controls $performance_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store controls_performance

// 模型5：加入市场表现控制变量（完整模型）
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls $governance_controls $ownership_controls $performance_controls $market_controls, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store controls_full

// 导出逐步加入控制变量的结果（完整版本）
esttab controls_baseline controls_governance controls_ownership controls_performance controls_full ///
    using "robustness_progressive_controls_full.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("稳健性检验：逐步加入更多控制变量（完整结果）") ///
    mtitles("基准模型" "+公司治理" "+股权结构" "+经营绩效" "+市场表现") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：基准模型包含企业基本特征和地区控制变量；" ///
             "公司治理变量包括董事会规模、独立董事比例、两职合一、四大审计、审计意见；" ///
             "股权结构变量包括机构投资者持股、管理层持股、大股东占用、第一大股东持股；" ///
             "经营绩效变量包括ROA、资产周转率、收入增长率、现金流、管理费用率；" ///
             "市场表现变量包括账面市值比、上市年龄；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

// 导出核心变量系数对比表（简化版本）
esttab controls_baseline controls_governance controls_ownership controls_performance controls_full ///
    using "robustness_progressive_controls_core.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("稳健性检验：核心解释变量系数稳定性") ///
    mtitles("基准模型" "+公司治理" "+股权结构" "+经营绩效" "+市场表现") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    keep(climateRiskPublicView) ///
    addnotes("注：表格仅显示核心解释变量系数以观察其稳定性；" ///
             "如果系数在各模型中保持稳定且显著，说明结果稳健；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

/*------------------------------------------------------------------------------
* 第十部分：全面的异质性分析
*------------------------------------------------------------------------------*/

// 异质性分析1：按企业规模分组（基于firmSize的中位数）
summarize firmSize, detail
local median_size = r(p50)
gen large_firm = (firmSize > `median_size')

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if large_firm==1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_large

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if large_firm==0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_small

// 异质性分析2：按国有企业性质分组
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if stateOwned==1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_soe

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if stateOwned==0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_nonsoe

// 异质性分析3：按行业污染程度分组
// 生成高污染行业虚拟变量（基于行业代码）
gen high_pollution = 0
// 重污染行业：采矿业、制造业中的化工、钢铁、有色金属、电力等
replace high_pollution = 1 if substr(industryCode,1,1) == "B" // 采矿业
replace high_pollution = 1 if substr(industryCode,1,3) == "C26" // 化学原料及化学制品制造业
replace high_pollution = 1 if substr(industryCode,1,3) == "C31" // 黑色金属冶炼及压延加工业
replace high_pollution = 1 if substr(industryCode,1,3) == "C32" // 有色金属冶炼及压延加工业
replace high_pollution = 1 if substr(industryCode,1,1) == "D" // 电力、热力、燃气及水生产和供应业

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if high_pollution==1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_pollute

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if high_pollution==0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_clean

// 异质性分析4：按企业年龄分组
summarize firmAge, detail
local median_age = r(p50)
gen mature_firm = (firmAge > `median_age')

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if mature_firm==1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_mature

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if mature_firm==0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_young

// 异质性分析5：按财务约束程度分组（基于企业规模和年龄）
gen financial_constrained = (large_firm==0 & mature_firm==0)

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if financial_constrained==1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_constrained

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if financial_constrained==0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_unconstrained

// 异质性分析6：按地区发展水平分组
summarize gdpPerCapita, detail
local median_gdp = r(p50)
gen developed_region = (gdpPerCapita > `median_gdp')

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if developed_region==1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_developed

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if developed_region==0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_underdeveloped

// 异质性分析7：按市场竞争程度分组（基于行业集中度代理变量）
// 使用托宾Q值作为市场竞争的代理变量
summarize tobinsQ1, detail
local median_q = r(p50)
gen high_competition = (tobinsQ1 > `median_q') // 高Q值通常表示高竞争或高增长机会

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if high_competition==1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_compete

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if high_competition==0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_monopoly

// 异质性分析8：按时间段分组（政策冲击前后）
// 以2015年《巴黎协定》为分界点
gen post_paris = (year >= 2015)

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if post_paris==1, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_post2015

reghdfe carbonIntensityRegional climateRiskPublicView $all_controls if post_paris==0, ///
    absorb(stockCode year) cluster(CITYCODE)
estimates store hetero_pre2015

// 导出异质性分析结果（分多个表格）
// 表1：基本企业特征异质性
esttab hetero_large hetero_small hetero_soe hetero_nonsoe using "heterogeneity_firm_basic.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("异质性分析：基本企业特征") ///
    mtitles("大企业" "小企业" "国有企业" "非国有企业") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：大企业定义为规模超过中位数的企业；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

// 表2：行业和年龄异质性
esttab hetero_pollute hetero_clean hetero_mature hetero_young using "heterogeneity_industry_age.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("异质性分析：行业污染程度与企业年龄") ///
    mtitles("高污染行业" "清洁行业" "成熟企业" "年轻企业") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：高污染行业包括采矿业、化工、钢铁、有色金属、电力等；" ///
             "成熟企业定义为年龄超过中位数的企业；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

// 表3：财务约束和地区发展异质性
esttab hetero_constrained hetero_unconstrained hetero_developed hetero_underdeveloped using "heterogeneity_finance_region.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("异质性分析：财务约束与地区发展水平") ///
    mtitles("财务约束企业" "非约束企业" "发达地区" "欠发达地区") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：财务约束企业定义为小规模且年轻的企业；" ///
             "发达地区定义为人均GDP超过中位数的地区；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

// 表4：市场竞争和时间异质性
esttab hetero_compete hetero_monopoly hetero_post2015 hetero_pre2015 using "heterogeneity_competition_time.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("异质性分析：市场竞争与时间维度") ///
    mtitles("高竞争市场" "低竞争市场" "2015年后" "2015年前") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：高竞争市场基于托宾Q值超过中位数定义；" ///
             "2015年为《巴黎协定》签署年份；" ///
             "括号内为聚类到城市层面的标准误；*、**、***分别表示在10%、5%、1%水平上显著")

/*------------------------------------------------------------------------------
* 第十一部分：可视化分析
*------------------------------------------------------------------------------*/

// 图1：核心变量的时间趋势图
preserve
collapse (mean) mean_carbon=carbonIntensityRegional (mean) mean_climate=climateRiskPublicView, by(year)

// 创建双轴图
twoway (line mean_carbon year, yaxis(1) lcolor(blue) lwidth(medium)) ///
       (line mean_climate year, yaxis(2) lcolor(red) lwidth(medium)), ///
       xlabel(2011(1)2022, angle(45)) ///
       ylabel(, axis(1) format(%9.3f)) ylabel(, axis(2) format(%9.3f)) ///
       ytitle("碳排放强度", axis(1)) ytitle("公众气候风险表达指数", axis(2)) ///
       xtitle("年份") ///
       legend(label(1 "碳排放强度") label(2 "公众气候风险表达指数")) ///
       title("核心变量时间趋势图") ///
       scheme(s1color)
graph export "time_trend.png", replace width(800) height(600)
restore

// 图2：散点图显示核心变量关系
preserve
// 为了避免过度拥挤，随机抽取部分样本
sample 20
scatter carbonIntensityRegional climateRiskPublicView if sample_main==1, ///
    mcolor(blue%60) msize(small) ///
    || lfit carbonIntensityRegional climateRiskPublicView if sample_main==1, ///
    lcolor(red) lwidth(medium) ///
    xlabel(, format(%9.2f)) ylabel(, format(%9.3f)) ///
    xtitle("公众气候风险表达指数") ytitle("碳排放强度") ///
    title("公众气候风险表达与碳排放强度关系图") ///
    legend(label(1 "观测值") label(2 "拟合线")) ///
    scheme(s1color)
graph export "scatter_plot.png", replace width(800) height(600)
restore

// 图3：机制变量的分布图
histogram csrScore if sample_main==1, ///
    bin(30) frequency fcolor(blue%60) lcolor(blue) ///
    xtitle("企业社会责任得分") ytitle("频数") ///
    title("企业社会责任得分分布图") ///
    scheme(s1color)
graph export "csr_distribution.png", replace width(800) height(600)

// 图4：调节效应可视化
preserve
// 为了绘制调节效应图，我们需要计算不同组别的平均效应
// 按绿色金融改革状态分组计算平均值
collapse (mean) mean_carbon=carbonIntensityRegional (mean) mean_climate=climateRiskPublicView ///
    (count) N=carbonIntensityRegional, by(greenFinanceReform)

// 创建调节效应对比图
graph bar mean_carbon, over(greenFinanceReform) ///
    bar(1, fcolor(blue%60) lcolor(blue)) ///
    ytitle("平均碳排放强度") ///
    title("绿色金融改革试验区的调节效应") ///
    legend(off) ///
    blabel(bar, format(%9.3f)) ///
    note("0=非试验区, 1=试验区") ///
    scheme(s1color)
graph export "moderation_effect_bar.png", replace width(800) height(600)

// 创建散点图显示调节效应
restore
preserve
// 随机抽样以避免图形过于拥挤
sample 10
// 按绿色金融改革状态分别绘制散点图
twoway (scatter carbonIntensityRegional climateRiskPublicView if greenFinanceReform==0, ///
        mcolor(blue%60) msize(small) msymbol(circle)) ///
       (scatter carbonIntensityRegional climateRiskPublicView if greenFinanceReform==1, ///
        mcolor(red%60) msize(small) msymbol(triangle)) ///
       (lfit carbonIntensityRegional climateRiskPublicView if greenFinanceReform==0, ///
        lcolor(blue) lwidth(medium)) ///
       (lfit carbonIntensityRegional climateRiskPublicView if greenFinanceReform==1, ///
        lcolor(red) lwidth(medium)), ///
       xlabel(, format(%9.2f)) ylabel(, format(%9.3f)) ///
       xtitle("公众气候风险表达指数") ytitle("碳排放强度") ///
       title("绿色金融改革的调节效应") ///
       legend(label(1 "非试验区观测值") label(2 "试验区观测值") ///
              label(3 "非试验区拟合线") label(4 "试验区拟合线")) ///
       scheme(s1color)
graph export "moderation_effect_scatter.png", replace width(800) height(600)
restore

/*------------------------------------------------------------------------------
* 第十二部分：综合结果汇总表
*------------------------------------------------------------------------------*/

// 创建主要结果汇总表
esttab model3 mechanism1_step2 mechanism2_step2 mechanism3_step2 ///
    moderator3 using "main_results_summary.rtf", replace ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    title("主要实证结果汇总") ///
    mtitles("基准回归" "CSR机制" "创新机制" "研发机制" "调节效应") ///
    scalars("N 观测值数" "r2_a 调整R方") ///
    addnotes("注：所有回归均控制了企业和年份固定效应，括号内为聚类到城市层面的标准误" ///
             "；*、**、***分别表示在10%、5%、1%水平上显著") ///
    keep(climateRiskPublicView csrScore citationExSelfCumulative rdIntensity ///
         greenFinanceReform qhsiyin climate_green_finance climate_adaptive_city)

/*------------------------------------------------------------------------------
* 第十三部分：数据质量检查和诊断
*------------------------------------------------------------------------------*/

// 检查多重共线性
// 由于reghdfe不支持estat vif，我们使用普通回归来检查VIF
reg climateRiskPublicView $all_controls if sample_main==1
estat vif

// 显示多重共线性检查结果
display "多重共线性检查完成。VIF值大于10表示存在严重多重共线性问题。"

// 额外检查：控制变量之间的相关系数矩阵
display "控制变量之间的相关系数矩阵："
pwcorr $all_controls if sample_main==1, sig star(.01) star5(.05) star10(.10)
display "相关系数绝对值大于0.8可能表示存在多重共线性问题。"
display "星号表示显著性：*10%、**5%、***1%"

// 检查残差的正态性
// 重新运行基准回归以获取残差（添加resid选项）
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls, ///
    absorb(stockCode year) cluster(CITYCODE) resid
predict residuals, residuals
histogram residuals, normal ///
    xtitle("残差") ytitle("密度") ///
    title("残差分布图") ///
    scheme(s1color)
graph export "residuals_distribution.png", replace width(800) height(600)

// 检查异方差
predict fitted_values, xb
scatter residuals fitted_values, ///
    mcolor(blue%60) msize(small) ///
    yline(0, lcolor(red)) ///
    xtitle("拟合值") ytitle("残差") ///
    title("残差vs拟合值图") ///
    scheme(s1color)
graph export "residuals_vs_fitted.png", replace width(800) height(600)

// 正态性检验
sktest residuals
display "正态性检验结果：p值小于0.05表示残差不服从正态分布"

// 异方差检验（使用Breusch-Pagan检验的替代方法）
// 由于reghdfe不直接支持异方差检验，我们通过残差图进行视觉检查
display "异方差检验：请查看残差vs拟合值图，如果残差呈现明显的扇形或其他模式，则可能存在异方差"

/*------------------------------------------------------------------------------
* 第十四部分：结果解读和政策含义
*------------------------------------------------------------------------------*/

// 计算经济显著性
reghdfe carbonIntensityRegional climateRiskPublicView $all_controls, absorb(stockCode year) cluster(CITYCODE)
local coef = _b[climateRiskPublicView]
summarize climateRiskPublicView if e(sample), detail
local sd_x = r(sd)
summarize carbonIntensityRegional if e(sample), detail
local sd_y = r(sd)
local beta = `coef' * `sd_x' / `sd_y'

display "标准化系数（Beta）: " `beta'
display "解释：公众气候风险表达指数增加1个标准差，碳排放强度降低 " abs(`beta') " 个标准差"

// 计算边际效应的经济意义
local marginal_effect = `coef' * `sd_x'
display "边际效应：公众气候风险表达指数增加1个标准差，碳排放强度平均降低 " abs(`marginal_effect') " 个单位"

/*------------------------------------------------------------------------------
* 第十五部分：清理和结束
*------------------------------------------------------------------------------*/

// 清理临时变量
drop if missing(sample_main)
drop L_climateRiskPublicView L_citationTotalCumulative L_citationExSelfCumulative L_carbonIntensity
drop large_firm climate_green_finance climate_adaptive_city
drop high_climate_risk pscore placebo_climate
drop high_pollution mature_firm financial_constrained developed_region high_competition post_paris
drop residuals fitted_values

// 保存最终数据集
save "final_analysis_data.dta", replace

// 关闭日志
log close

// 显示完成信息
display "分析完成！所有结果已保存到相应文件中。"
display "主要输出文件："
display "1. descriptive_stats.doc - 描述性统计"
display "2. correlation_matrix.doc - 相关系数矩阵"
display "3. baseline_regression.rtf - 基准回归结果"
display "4. mechanism_analysis_step1.rtf - 机制分析第一步结果"
display "5. mechanism_analysis_step2.rtf - 机制分析第二步结果"
display "6. moderator_analysis.rtf - 调节效应分析"
display "7. robustness_tobinq.rtf - Tobin Q稳健性检验"
display "8. robustness_rd_mechanism.rtf - 研发投入机制变量稳健性检验"
display "9. robustness_innovation_mechanism.rtf - 创新质量机制变量稳健性检验"
display "10. robustness_lagged_variable.rtf - 滞后解释变量稳健性检验"
display "11. robustness_advanced.rtf - 高级稳健性检验(PSM、GMM)"
display "12. robustness_progressive_controls_full.rtf - 逐步加入控制变量稳健性检验（完整结果）"
display "13. robustness_progressive_controls_core.rtf - 核心解释变量系数稳定性检验"
display "14. heterogeneity_firm_basic.rtf - 基本企业特征异质性分析"
display "15. heterogeneity_industry_age.rtf - 行业污染程度与企业年龄异质性"
display "16. heterogeneity_finance_region.rtf - 财务约束与地区发展异质性"
display "17. heterogeneity_competition_time.rtf - 市场竞争与时间维度异质性"
display "18. main_results_summary.rtf - 主要结果汇总"
display "19. placebo_test_distribution.png - 安慰剂检验结果分布图"
display "20. 其他各类图表文件(.png格式)"

/*==============================================================================
* 分析结束
*==============================================================================*/
