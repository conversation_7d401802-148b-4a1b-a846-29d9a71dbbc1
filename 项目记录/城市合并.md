  ___  ____  ____  ____  ____ ®
 /__    /   ____/   /   ____/      18.0
___/   /   /___/   /   /___/       MP—Parallel Edition

 Statistics and Data Science       Copyright 1985-2023 StataCorp LLC
                                   StataCorp
                                   4905 Lakeway Drive
                                   College Station, Texas 77845 USA
                                   800-STATA-PC        https://www.stata.c

> om
> 979-696-4600        <EMAIL>

Stata license: Single-user 2-core  perpetual
Serial number: 501806366048
  Licensed to: 蔡炫宇
               1

Notes:
      1. Unicode is supported; see help unicode_advice.
      2. More than 2 billion observations are allowed; see help
          obs_advice.
      3. Maximum number of variables is set to 5,000 but can be
          increased; see help set_maxvar.

Running /Applications/Stata/profile.do ...

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/市级年度气候公众表达.dta"

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

---

Copies | Observations       Surplus
----------+---------------------------
        1 |         4237             0
--------------------------------------

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/绿色金融改革试点.dta"

. cd "/Users/<USER>/Desktop/clitansz/原始dta数据集"
/Users/<USER>/Desktop/clitansz/原始dta数据集

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

---

Copies | Observations       Surplus
----------+---------------------------
        1 |         4074             0
        2 |          198            99
        3 |           84            56
--------------------------------------

. merge m:1 CITYCODE year using 市级年度气候公众表达.dta

| Result                      Number of obs          |
| -------------------------------------------------- |
| Not matched                           936          |
| from master                       460  (_merge==1) |
| from using                        476  (_merge==2) |

    Matched                             3,896  (_merge==3)
    -----------------------------------------

. replace 绿色金融改革创新试验区 = 0 if inlist(CITYCODE, 152200, 152500, 152900, 222400, 232700, 422800, 433100
too few ')' or ']'
r(132);

. replace 绿色金融改革创新试验区 = 0 if inlist(CITYCODE, 152200, 152500, 152900, 222400, 232700, 422800, 433100, 513

> 200, 513300, 513400, 522300, 522600, 522700, 532300, 532500, 532600, 532800, 532900, 533100, 533300, 533400, 54250
> 0, 622900, 623000, 632200, 632300, 632600, 632700, 632800, 652700, 652800, 652900, 653000, 653100, 653200, 654000,
> 654200, 654300)
> (464 real changes made)

. replace 绿色金融改革创新试验区 = 1 if CITYCODE == 652300
(12 real changes made)

. drop if _merge == 1
(460 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金.dta"
file /Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金.dta saved

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/环保法庭.dta"

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

---

Copies | Observations       Surplus
----------+---------------------------
        1 |         5415             0
--------------------------------------

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金.dta"

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

---

Copies | Observations       Surplus
----------+---------------------------
        1 |         4126             0
        2 |          174            87
        3 |           72            48
--------------------------------------

. merge m:1 CITYCODE year using "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金.dta"
variables CITYCODE year do not uniquely identify observations in the using data
r(459);

. merge m:1 CITYCODE year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/环保法庭.dta"

| Result                      Number of obs          |
| -------------------------------------------------- |
| Not matched                         2,476          |
| from master                       660  (_merge==1) |
| from using                      1,816  (_merge==2) |

    Matched                             3,712  (_merge==3)
    -----------------------------------------

.
. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         5942             0
        2 |          174            87
        3 |           72            48
--------------------------------------

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金法庭.dta"
file /Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金法庭.dta saved

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/宽带中国.dta"

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         7200             0
--------------------------------------

. 
. 
. rename 最早试点年份 kdyear

. label ver kdyear 宽带中国最早试点年份
invalid syntax
r(198);

. label var kdyear 宽带中国最早试点年份

. rename DID kuandai

. label var kuandai 宽带中国政策DID

. drop K

. rename 长江经济带 canjian

. rename 试点城市 sdcs

. rename 胡焕庸线 hhyx

. save "/Users/<USER>/Desktop/clitansz/原始dta数据集/宽带中国.dta", replace
file /Users/<USER>/Desktop/clitansz/原始dta数据集/宽带中国.dta saved

. merge m:1 CITYCODE year using "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金法庭.dta"
(variable 地区 was str15, now str30 to accommodate using data's values)
variable _merge already defined
r(110);

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金法庭.dta", clear

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金法庭.dta", replace
file /Users/<USER>/Desktop/clitansz/合并中数据集/气候绿金法庭.dta saved

. merge m:1 CITYCODE year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/宽带中国.dta"

    Result                      Number of obs
    -----------------------------------------
    Not matched                         2,099
        from master                       476  (_merge==1)
        from using                      1,623  (_merge==2)

    Matched                             5,712  (_merge==3)
    -----------------------------------------

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta"
file /Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta saved

. 












