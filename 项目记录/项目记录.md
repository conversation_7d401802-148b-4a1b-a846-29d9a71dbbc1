## 数据集对应来源：

1. 上市公司所处城市.dta(2000-2024)→国泰安数据库
2. 上市公司碳绩效与碳强度（2000-2023年）.dta→闲鱼/马克数据网
3. 市级年度气候公众表达.dta→全球气候风险综合集成数据库（GCRID）
4. ControlVarsDetail.dta→onestata公众号
5. 绿色金融改革试点.dta→闲鱼

## 合并记录：

### 上市公司与上市公司合并：stkcd year
. cd "/Users/<USER>/Desktop/clipubtan"
. use "/Users/<USER>/Desktop/clipubtan/上市公司所处城市.dta"

. duplicates report stkcd year


Duplicates in terms of stkcd year

Copies | Observations       Surplus
----------+---------------------------
        1 |        68851             0
--------------------------------------

. list stkcd year if floor(year)!=year




. use "/Users/<USER>/Desktop/clipubtan/上市公司碳绩效与碳强度（2000-2023年）.dta"

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        42191             0
--------------------------------------


. list stkcd year if floor(year)!=year


. distinct stkcd

------------------------------
       |     total   distinct
-------+----------------------
 stkcd |     42191       3444
------------------------------





. merge 1:1 stkcd year using 上市公司所处城市.dta
(variable year was int, now float to accommodate using data's values)

    Result                      Number of obs
    -----------------------------------------
    Not matched                        26,728
        from master                        34  (_merge==1)
        from using                     26,694  (_merge==2)

    Matched                            42,157  (_merge==3)
    -----------------------------------------


_merge==1里为招商局B（200024）、小天鹅B（200418），可以剔除
_merge==2里为只在“上市公司所处城市.dta“里有，可以剔除

. keep if _merge==3
(26,728 observations deleted)

. drop _merge


. save "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市.dta"
file /Users/<USER>/Desktop/clipubtan/碳绩效+所在市.dta saved


. use "/Users/<USER>/Desktop/clipubtan/ControlVarsDetail.dta"
(Commonly used control variables (2001-2023) by Shutter Zor (Shutter_Z@outlook).)

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        65435             0
--------------------------------------

. list stkcd year if floor(year)!=year


. merge 1:1 stkcd year using 碳绩效+所在市.dta

    Result                      Number of obs
    -----------------------------------------
    Not matched                        24,600
        from master                    23,939  (_merge==1)
        from using                        661  (_merge==2)

    Matched                            41,496  (_merge==3)
    -----------------------------------------

_merge==1里为只在“ControlVarsDetail.dta“里有，可以剔除（金融业等）
_merge==2里为2000年的数据，没有控制变量，可以剔除


. keep if _merge==3
(24,600 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市+控制变量.dta"
file /Users/<USER>/Desktop/clipubtan/碳绩效+所在市+控制变量.dta saved


### 城市数据与城市数据合并：CITYCODE year
use "/Users/<USER>/Desktop/clipubtan/市级年度气候公众表达.dta"


. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         4237             0
--------------------------------------


. list CITYCODE year if floor(year)!=year


distinct CITYCODE

. distinct CITYCODE

---------------------------------
          |     total   distinct
----------+----------------------
 CITYCODE |      4237        337
---------------------------------

. use "/Users/<USER>/Desktop/clipubtan/绿色金融改革试点.dta"

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         4074             0
        2 |          198            99
        3 |           84            56
--------------------------------------

所属省份	地区
河北省	衡水市
河北省	雄安新区

所属省份	地区
内蒙古自治区	呼伦贝尔市
内蒙古自治区	满洲里市

所属省份	地区
吉林省	珲春市
吉林省	白城市

所属省份	地区
黑龙江省	绥芬河市
黑龙江省	牡丹江市

所属省份	地区
浙江省	丽水市
浙江省	义乌市

所属省份	地区
江西省	赣江新区
江西省	南昌市

所属省份	地区
贵州省	贵安新区
贵州省	贵阳市

所属省份	地区
云南省	红河哈尼族彝族自治州
云南省	德宏傣族景颇族自治州
云南省	临沧市

所属省份	地区
新疆维吾尔自治区	哈密市
新疆维吾尔自治区	阿拉山口市
新疆维吾尔自治区	喀什地区


- 所以选择了m:1的合并方式

. merge m:1 CITYCODE year using 市级年度气候公众表达.dta

    Result                      Number of obs
    -----------------------------------------
    Not matched                           936
        from master                       460  (_merge==1)
        from using                        476  (_merge==2)

    Matched                             3,896  (_merge==3)
    -----------------------------------------

_merge==2的绿色金融改革试点缺失值：
. levelsof CITYCODE
152200 152500 152900 222400 232700 422800 433100 513200 513300 513400 522300 522600 522700 532300 532500 532600 532800 532900 533100 533300 533400 542500 622900 623000 632200 632300 632600 632700 632800 652300 652700 652800 652900 653000 653100 653200 654000 654200 654300


_merge==1里为只在“绿色金融改革试点.dta“里有，这些城市没有相应的气候公众表达数据，可以剔除
-_merge==2里为各种自治州、盟。地区，人工核查后发现，652300：昌吉回族自治州（地级）在2017年加入试点，其他地区并没有加入，于是手动进行01赋值



. replace 绿色金融改革创新试验区 = 0 if inlist(CITYCODE, 152200, 152500, 152900, 222400, 232700, 422800, 433100, 
> 513200, 513300, 513400, 522300, 522600, 522700, 532300, 532500, 532600, 532800, 532900, 533100, 533300, 533400,
>  542500, 622900, 623000, 632200, 632300, 632600, 632700, 632800, 652700, 652800, 652900, 653000, 653100, 653200
> , 654000, 654200, 654300)
(464 real changes made)


. replace 绿色金融改革创新试验区 = 1 if CITYCODE == 652300
(12 real changes made)


. drop if _merge == 1
(460 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clipubtan/clipub+zc.dta"
file /Users/<USER>/Desktop/clipubtan/clipub+zc.dta saved


### 公司数据与城市数据合并：CITYCODE year

. use "/Users/<USER>/Desktop/clipubtan/clipub+zc.dta"

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         4126             0
        2 |          174            87
        3 |           72            48
--------------------------------------


. use "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市+控制变量.dta"
(Commonly used control variables (2001-2023) by Shutter Zor (Shutter_Z@outlook).)

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         2991             0
        2 |         2074          1037
        3 |         1986          1324
        4 |         1648          1236
        5 |         1305          1044
        6 |         1380          1150
        7 |          994           852
        8 |          952           833
        9 |          738           656
       10 |          840           756
       11 |          825           750
       12 |          780           715
       13 |          832           768
       14 |          630           585
       15 |          555           518
       16 |          592           555
       17 |          867           816
       18 |          702           663
       19 |          323           306
       20 |          660           627
       21 |          609           580
       22 |          462           441
       23 |          322           308
       24 |          216           207
       25 |          325           312
       26 |          312           300
       27 |          405           390
       28 |          252           243
       29 |          319           308
       30 |          150           145
       31 |          372           360
       32 |          288           279
       33 |          264           256
       34 |          238           231
       35 |          210           204
       36 |          252           245
       37 |          148           144
       38 |          190           185
       39 |          117           114
       40 |          400           390
       41 |          205           200
       42 |          168           164
       43 |          430           420
       44 |          220           215
       45 |          270           264
       46 |          368           360
       47 |          376           368
       48 |          240           235
       49 |          147           144
       50 |          200           196
       51 |          102           100
       53 |          106           104
       54 |          108           106
       55 |          110           108
       57 |          114           112
       58 |          174           171
       59 |           59            58
       62 |           62            61
       65 |          130           128
       66 |          198           195
       68 |          136           134
       69 |          207           204
       70 |           70            69
       71 |          142           140
       73 |           73            72
       74 |          222           219
       76 |           76            75
       77 |           77            76
       78 |          156           154
       79 |           79            78
       81 |          162           160
       84 |          252           249
       85 |          255           252
       86 |           86            85
       87 |           87            86
       88 |          176           174
       90 |           90            89
       95 |           95            94
       96 |          192           190
       97 |           97            96
       99 |           99            98
      108 |          108           107
      113 |          226           224
      119 |          119           118
      120 |          240           238
      124 |          248           246
      125 |          250           248
      126 |          252           250
      127 |          127           126
      128 |          128           127
      131 |          393           390
      143 |          143           142
      150 |          150           149
      153 |          306           304
      155 |          155           154
      160 |          160           159
      164 |          164           163
      174 |          174           173
      179 |          179           178
      185 |          185           184
      189 |          189           188
      192 |          192           191
      196 |          196           195
      201 |          201           200
      208 |          624           621
      209 |          209           208
      229 |          229           228
      238 |          238           237
      255 |          255           254
      257 |          257           256
      258 |          258           257
      266 |          266           265
      267 |          534           532
--------------------------------------


- 多对多，进行m:m匹配

. list CITYCODE if CITYCODE == "None"

       +----------+
       | CITYCODE |
       |----------|
41420. | None     |
41421. | None     |
41422. | None     |
41423. | None     |
41424. | None     |
       |----------|
41425. | None     |
41426. | None     |
41427. | None     |
41428. | None     |
41429. | None     |
       |----------|
41430. | None     |
41431. | None     |
41432. | None     |
41433. | None     |
41434. | None     |
       |----------|
41435. | None     |
41436. | None     |
41437. | None     |
41438. | None     |
41439. | None     |
       |----------|
41440. | None     |
41441. | None     |
41442. | None     |
41443. | None     |
41444. | None     |
       |----------|
41445. | None     |
41446. | None     |
41447. | None     |
41448. | None     |
41449. | None     |
       |----------|
41450. | None     |
41451. | None     |
41452. | None     |
41453. | None     |
41454. | None     |
       |----------|
41455. | None     |
41456. | None     |
41457. | None     |
41458. | None     |
41459. | None     |
       |----------|
41460. | None     |
41461. | None     |
41462. | None     |
41463. | None     |
41464. | None     |
       |----------|
41465. | None     |
41466. | None     |
41467. | None     |
41468. | None     |
41469. | None     |
       |----------|
41470. | None     |
41471. | None     |
41472. | None     |
41473. | None     |
41474. | None     |
       |----------|
41475. | None     |
41476. | None     |
41477. | None     |
41478. | None     |
41479. | None     |
       |----------|
41480. | None     |
41481. | None     |
41482. | None     |
41483. | None     |
41484. | None     |
       |----------|
41485. | None     |
41486. | None     |
41487. | None     |
41488. | None     |
41489. | None     |
       |----------|
41490. | None     |
41491. | None     |
41492. | None     |
41493. | None     |
41494. | None     |
       |----------|
41495. | None     |
41496. | None     |
       +----------+


发现其中襄阳市、林芝市、海安市、宁乡市的行政区划代码是None, 进行补全
其中开曼群岛的行政区划代码是None, 进行剔除

海安市在部分资料中显示代码为320621（旧县级代码），但2018年撤县设市后统一使用320685

![1748853271261](image/项目记录/1748853271261.png)


襄阳市 (Xiangyang City, Hubei Province): 420600
林芝市 (Nyingchi/Linzhi City, Tibet Autonomous Region): 540400
海安市 (Hai'an City, Jiangsu Province): 320685 (海安市是江苏省南通市代管的县级市)
宁乡市 (Ningxiang City, Hunan Province): 430182 (宁乡市是湖南省长沙市代管的县级市)


. replace CITYCODE = "420600" if inlist(CITY, "襄阳市")
(40 real changes made)

. replace CITYCODE = "540400" if inlist(CITY, "林芝市")
(6 real changes made)

. replace CITYCODE = "320685" if inlist(CITY, "海安市")
(8 real changes made)

. replace CITYCODE = "430182" if inlist(CITY, "宁乡市")
(3 real changes made)

. drop if CITYCODE == "None"
(20 observations deleted)


. destring CITYCODE, replace
CITYCODE: all characters numeric; replaced as long

. merge m:m CITYCODE year using clipub+zc.dta

    Result                      Number of obs
    -----------------------------------------
    Not matched                        14,776
        from master                    13,412  (_merge==1)
        from using                      1,364  (_merge==2)

    Matched                            28,108  (_merge==3)
    -----------------------------------------


发现不对，不应该使用m:m，于是重新使用CITYCODE CITY year三变量进行匹配



. merge m:1 CITYCODE CITY year using clipub+zc.dta

    Result                      Number of obs
    -----------------------------------------
    Not matched                        15,084
        from master                    13,542  (_merge==1)
        from using                      1,542  (_merge==2)

    Matched                            27,934  (_merge==3)
    -----------------------------------------

_merge==1里为年份里为2001-2010的数据，理应剔除

keep if _merge==1

. keep if year > 2010
(9,655 observations deleted)

_merge==1里为年份里为2001-2010的数据有9,655条，理应剔除
13,542-9,655里为市级数据缺失，没办法
_merge==2里为对应市没有符合的上市公司，理应剔除


. sort stkcd year

. save "/Users/<USER>/Desktop/clipubtan/all_nondrop.dta"
file /Users/<USER>/Desktop/clipubtan/all_nondrop.dta saved

. keep if _merge == 3
(15,084 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clipubtan/all.dta"
file /Users/<USER>/Desktop/clipubtan/all.dta saved



对碳绩效数据进行原始分析，发现其企业只有3,446家
. egen n_stkcd = tag(id)

. count if n_stkcd==1
  3,446


缺失主要在这里


* 剔除有过st，*st的个体
. use "/Users/<USER>/Desktop/clipubtan/all.dta", clear
* 生成一个标记变量，标记STKNM中包含"ST"的行
. gen is_st = strpos(STKNM, "ST") > 0
* 找出所有出现过"ST"的stkcd
. bysort stkcd: egen any_st = max(is_st)
* 保留没有出现过"ST"的stkcd
. keep if any_st == 0
(2,793 observations deleted)
* 删除临时变量
. drop is_st any_st
* 保存处理后的数据
. save "/Users/<USER>/Desktop/clipubtan/all.dta", replace
file /Users/<USER>/Desktop/clipubtan/all.dta saved



. gen is_bank = strpos(STKNM, "银行") > 0

. list stkcd STKNM if is_bank

. drop is_bank

数据本身就剔除了金融业


. egen n_stkcd = tag(stkcd)

. count if n_stkcd==1
  2,826

. drop n_stkcd

. gen is_b = strpos(STKNM, "B") > 0

. bysort stkcd: egen any_b = max(is_b)

. keep if any_b == 0
(99 observations deleted)

. drop is_b any_b

. egen n_stkcd = tag(stkcd)

. count if n_stkcd==1
  2,815

. drop n_stkcd

剔除了B股后还剩下2,815家上市公司


. keep if year >= 2011 & year <= 2023
(0 observations deleted)

. bysort stkcd: egen minyear = min(year)

. bysort stkcd: egen maxyear = max(year)

. bysort stkcd: gen n = _N

. gen should_have = 2023 - minyear + 1

. gen is_complete = (n == should_have)

. list stkcd minyear maxyear n should_have if is_complete == 0


. tab is_complete

is_complete |      Freq.     Percent        Cum.
------------+-----------------------------------
          0 |        572        2.28        2.28
          1 |     24,470       97.72      100.00
------------+-----------------------------------
      Total |     25,042      100.00













preserve
restore