### 上市公司与上市公司合并：stkcd year



. use "/Users/<USER>/Downloads/上市公司碳排放衍生计算表（年）145921581(仅供山东财经大学使用)/CNE_CemissDerive.dta"
(CSMAR)

. format Revenue %15.0f

. gen tanqd = Revenue / 1000000
(387 missing values generated)

. gen tanjx = tanqd / CEmission
(653 missing values generated)

. label variable tanjx "单位碳排放营业收入（百万元/吨）(碳绩效)"

. label variable tanqd "营业收入（百万元）"

. rename tanqd yysr

. save "/Users/<USER>/Desktop/clipubtan/碳绩效.dta"
file /Users/<USER>/Desktop/clipubtan/碳绩效.dta saved


.  gen year = year(date(EndDate, "YMD"))

. sort stkcd year

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        68851             0
--------------------------------------

. use "/Users/<USER>/Desktop/clipubtan/碳绩效.dta"
(CSMAR)

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        27998             0
--------------------------------------

. distinct stkcd

------------------------------
       |     total   distinct
-------+----------------------
 stkcd |     27998       3685
------------------------------


. destring stkcd, replace
stkcd: all characters numeric; replaced as long



. merge 1:1 stkcd year using 上市公司所处城市.dta

    Result                      Number of obs
    -----------------------------------------
    Not matched                        40,853
        from master                         0  (_merge==1)
        from using                     40,853  (_merge==2)

    Matched                            27,998  (_merge==3)
    -----------------------------------------



. keep if _merge == 3
(40,853 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市2.dta"
file /Users/<USER>/Desktop/clipubtan/碳绩效+所在市2.dta saved

_merge==1里为只在“ControlVarsDetail.dta“里有，可以剔除（金融业等）
_merge==2里为2000年的数据，没有控制变量，可以剔除


. keep if _merge == 3
(24,600 observations deleted)

. drop _merge


. save "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市+控制变量2.dta"
file /Users/<USER>/Desktop/clipubtan/碳绩效+所在市+控制变量2.dta saved

use "/Users/<USER>/Desktop/clipubtan/ControlVarsDetail.dta"

. merge 1:1 stkcd year using 碳绩效+所在市2.dta

    Result                      Number of obs
    -----------------------------------------
    Not matched                        37,467
        from master                    37,452  (_merge==1)
        from using                         15  (_merge==2)

    Matched                            27,983  (_merge==3)
    -----------------------------------------





















































  ___  ____  ____  ____  ____ ®
 /__    /   ____/   /   ____/      18.0
___/   /   /___/   /   /___/       MP—Parallel Edition

 Statistics and Data Science       Copyright 1985-2023 StataCorp LLC
                                   StataCorp
                                   4905 Lakeway Drive
                                   College Station, Texas 77845 USA
                                   800-STATA-PC        https://www.stata.com
                                   979-696-4600        <EMAIL>

Stata license: Single-user 2-core  perpetual
Serial number: 501806366048
  Licensed to: 蔡炫宇
               1

Notes:
      1. Unicode is supported; see help unicode_advice.
      2. More than 2 billion observations are allowed; see help obs_advice.
      3. Maximum number of variables is set to 5,000 but can be increased; see help set_maxvar.

Running /Applications/Stata/profile.do ...

. use "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市+控制变量2.dta"
(Commonly used control variables (2001-2023) by Shutter Zor (Shutter_Z@outlook).)

. use "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市+控制变量.dta"
(Commonly used control variables (2001-2023) by Shutter Zor (Shutter_Z@outlook).)

. use "/Users/<USER>/Desktop/clipubtan/碳绩效.dta"
(CSMAR)

. use "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市.dta"
(【马克数据网】)

. use "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市2.dta"
(CSMAR)

. use "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市2.dta"
(CSMAR)

. use "/Users/<USER>/Desktop/clipubtan/ControlVarsDetail.dta"
(Commonly used control variables (2001-2023) by Shutter Zor (Shutter_Z@outlook).)

. merge 1:1 stkcd year using 碳绩效+所在市2.dta

    Result                      Number of obs
    -----------------------------------------
    Not matched                        37,467
        from master                    37,452  (_merge==1)
        from using                         15  (_merge==2)

    Matched                            27,983  (_merge==3)
    -----------------------------------------

. keep if _merge == 3
(37,467 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clipubtan/碳绩效+所在市+控制变量2.dta"
file /Users/<USER>/Desktop/clipubtan/碳绩效+所在市+控制变量2.dta saved

. replace CITYCODE = "420600" if inlist(CITY, "襄阳市")
(0 real changes made)

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         1786             0
        2 |         1464           732
        3 |         1275           850
        4 |         1044           783
        5 |         1100           880
        6 |          906           755
        7 |          665           570
        8 |          624           546
        9 |          414           368
       10 |          450           405
       11 |          561           510
       12 |          444           407
       13 |          507           468
       14 |          546           507
       15 |          600           560
       16 |          656           615
       17 |          527           496
       18 |          666           629
       19 |          228           216
       20 |          220           209
       21 |          336           320
       22 |          374           357
       23 |          276           264
       24 |          384           368
       25 |          225           216
       26 |          260           250
       27 |          108           104
       28 |           84            81
       29 |          377           364
       30 |          210           203
       31 |          248           240
       32 |          224           217
       33 |          297           288
       34 |          136           132
       35 |          175           170
       36 |          108           105
       37 |          407           396
       38 |          228           222
       40 |          280           273
       41 |          246           240
       42 |           42            41
       43 |          258           252
       44 |           44            43
       45 |          180           176
       46 |          230           225
       47 |           94            92
       48 |           96            94
       49 |          147           144
       50 |          150           147
       51 |           51            50
       55 |          110           108
       57 |          114           112
       58 |          116           114
       61 |           61            60
       62 |           62            61
       65 |           65            64
       66 |           66            65
       67 |           67            66
       69 |           69            68
       70 |           70            69
       71 |           71            70
       72 |          144           142
       74 |           74            73
       75 |           75            74
       78 |          156           154
       79 |           79            78
       81 |           81            80
       82 |           82            81
       85 |           85            84
       86 |           86            85
       88 |           88            87
       89 |           89            88
       90 |           90            89
       94 |           94            93
       95 |          285           282
       96 |          192           190
       97 |           97            96
       98 |           98            97
      100 |          100            99
      101 |          101           100
      103 |          103           102
      106 |          106           105
      112 |          112           111
      115 |          115           114
      118 |          118           117
      120 |          120           119
      122 |          122           121
      126 |          126           125
      128 |          128           127
      131 |          131           130
      132 |          132           131
      138 |          138           137
      145 |          145           144
      150 |          150           149
      153 |          153           152
      156 |          156           155
      163 |          163           162
      171 |          171           170
      176 |          176           175
      180 |          180           179
      193 |          193           192
      203 |          203           202
      208 |          208           207
      228 |          228           227
      229 |          229           228
      252 |          252           251
--------------------------------------

. list CITYCODE if CITYCODE == "None"

       +----------+
       | CITYCODE |
       |----------|
 6182. | None     |
 6183. | None     |
 6184. | None     |
 6185. | None     |
 6186. | None     |
       |----------|
 7189. | None     |
 7190. | None     |
 7191. | None     |
 7192. | None     |
 7193. | None     |
       |----------|
 7194. | None     |
 7195. | None     |
 7196. | None     |
 9575. | None     |
 9576. | None     |
       |----------|
14140. | None     |
14141. | None     |
14142. | None     |
14143. | None     |
24349. | None     |
       |----------|
27233. | None     |
27234. | None     |
27464. | None     |
27465. | None     |
27466. | None     |
       |----------|
27467. | None     |
27493. | None     |
27749. | None     |
27750. | None     |
27790. | None     |
       |----------|
27791. | None     |
27792. | None     |
27793. | None     |
27794. | None     |
27795. | None     |
       +----------+

. drop if CITYCODE == "None"
(35 observations deleted)

. destring CITYCODE, replace
CITYCODE: all characters numeric; replaced as long

. merge m:1 CITYCODE CITY year using clipub+zc.dta

    Result                      Number of obs
    -----------------------------------------
    Not matched                         6,604
        from master                     4,883  (_merge==1)
        from using                      1,721  (_merge==2)

    Matched                            23,065  (_merge==3)
    -----------------------------------------

. keep if _merge==3
(6,604 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clipubtan/all3.dta"
file /Users/<USER>/Desktop/clipubtan/all3.dta saved

. gen is_st = strpos(STKNM, "ST") > 0

. bysort stkcd: egen any_st = max(is_st)

. keep if any_st == 0
(2,953 observations deleted)

. drop is_st any_st

. save "/Users/<USER>/Desktop/clipubtan/all3.dta", replace
file /Users/<USER>/Desktop/clipubtan/all3.dta saved

. . gen is_bank = strpos(STKNM, "银行") > 0

. list stkcd STKNM if is_bank

. drop is_bank

. egen n_stkcd = tag(stkcd)

. count if n_stkcd==1
  2,932

. drop n_stkcd

. gen is_b = strpos(STKNM, "B") > 0

. bysort stkcd: egen any_b = max(is_b)

. keep if any_b == 0
(0 observations deleted)

. drop is_b any_b

. sort stkcd year

. 
. keep if year >= 2011 & year <= 2022
(0 observations deleted)

. bysort stkcd: egen minyear = min(year)

. bysort stkcd: egen maxyear = max(year)

. bysort stkcd: gen n = _N

. gen should_have = 2022 - minyear + 1

. gen is_complete = (n == should_have)

. tab is_complete

is_complete |      Freq.     Percent        Cum.
------------+-----------------------------------
          0 |      1,084        5.39        5.39
          1 |     19,028       94.61      100.00
------------+-----------------------------------
      Total |     20,112      100.00














