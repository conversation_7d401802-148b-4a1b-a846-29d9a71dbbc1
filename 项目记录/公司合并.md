
  ___  ____  ____  ____  ____ ®
 /__    /   ____/   /   ____/      18.0
___/   /   /___/   /   /___/       MP—Parallel Edition

 Statistics and Data Science       Copyright 1985-2023 StataCorp LLC
                                   StataCorp
                                   4905 Lakeway Drive
                                   College Station, Texas 77845 USA
                                   800-STATA-PC        https://www.stata.c
> om
                                   979-696-4600        <EMAIL>

Stata license: Single-user 2-core  perpetual
Serial number: 501806366048
  Licensed to: 蔡炫宇
               1

Notes:
      1. Unicode is supported; see help unicode_advice.
      2. More than 2 billion observations are allowed; see help
          obs_advice.
      3. Maximum number of variables is set to 5,000 but can be
          increased; see help set_maxvar.

Running /Applications/Stata/profile.do ...

. cd "/Users/<USER>/Desktop/clitansz/原始dta数据集"
/Users/<USER>/Desktop/clitansz/原始dta数据集

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/碳排放强度.dta"
(CSMAR)

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        27998             0
--------------------------------------

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/控制变量.dta"
(Commonly used control variables (2001-2023) by Shutter Zor (Shutter_Z@out
> look).)

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        65435             0
--------------------------------------

. merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/碳
> 排放强度.dta"
key variable stkcd is long in master but str6 in using data
    Each key variable (on which observations are matched) must be of the
    same generic type in the master and using datasets.  Same generic
    type means both numeric or both string.
r(106);

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/碳排放强度.dta"
(CSMAR)

. destring stkcd, replace
stkcd: all characters numeric; replaced as long

. save "/Users/<USER>/Desktop/clitansz/原始dta数据集/碳排放强度.dta", replace
file /Users/<USER>/Desktop/clitansz/原始dta数据集/碳排放强度.dta saved

. merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/控
> 制变量.dta"

    Result                      Number of obs
    -----------------------------------------
    Not matched                        37,467
        from master                        15  (_merge==1)
        from using                     37,452  (_merge==2)

    Matched                            27,983  (_merge==3)
    -----------------------------------------

. keep if _merge == 3
(37,467 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳强度控制.dta"
file /Users/<USER>/Desktop/clitansz/合并中数据集/碳强度控制.dta saved

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        27983             0
--------------------------------------

. 
. 
. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/绿色专利被引.dta"

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        98896             0
--------------------------------------

. merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/合并中数据集/碳
> 强度控制.dta"

    Result                      Number of obs
    -----------------------------------------
    Not matched                        78,461
        from master                    74,687  (_merge==1)
        from using                      3,774  (_merge==2)

    Matched                            24,209  (_merge==3)
    -----------------------------------------

. drop if _merge == 1
(74,687 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制专利.dta"
file /Users/<USER>/Desktop/clitansz/合并中数据集/碳控制专利.dta saved

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        27983             0
--------------------------------------

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/融资约束.dta"

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        58486             0
--------------------------------------

. merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/合并中数据集/碳
> 控制专利.dta"
(variable year was int, now float to accommodate using data's values)
(variable ShortName was str13, now str18 to accommodate using data's
       values)

    Result                      Number of obs
    -----------------------------------------
    Not matched                        32,159
        from master                    31,331  (_merge==1)
        from using                        828  (_merge==2)

    Matched                            27,155  (_merge==3)
    -----------------------------------------

. keep if _merge == 3
(32,159 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳强度控制融资.dta"
file /Users/<USER>/Desktop/clitansz/合并中数据集/碳强度控制融资.dta saved

. 
. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta"

. duplicates report stkcd year
variable stkcd not found
r(111);

. use "/Users/<USER>/Desktop/clitansz/数据构建参考文献/社会责任/CNDD-0071 上市公司社会责任研究数据/CNDD-0071 上市公司社会责任报告评价指标.dta
> "

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta"

. rename symbol stkcd

. gen year = substr(enddate, 1, 4)

. destring year, replace
year: all characters numeric; replaced as int

. gen sumshzr == worksafety + systemconstruction + staffprotection + shareholdersprotection + publicrelations + g
> ri + environmentprotectio + deliveryprotection + deficiency + customerprotection + creditorprotection + certifi
> cation
== invalid name
r(198);

. gen sumshzr == worksafety + systemconstruction + staffprotection + shareholdersprotection + publicrelations + g
> ri + environmentprotection + deliveryprotection + deficiency + customerprotection + creditorprotection + certif
> ication
== invalid name
r(198);

. 
. egen sumshzr = rowtotal(worksafety systemconstruction staffprotection shareholdersprotection ///
parentheses unbalanced
r(132);

.                        publicrelations gri environmentprotection deliveryprotection ///
command publicrelations is unrecognized
r(199);

.                        deficiency customerprotection creditorprotection certification)
command deficiency is unrecognized
r(199);

. do "/var/folders/80/yx4y3m9s2x19mghrsz2f8hkc0000gn/T//SD09960.000000"

. egen sumshzr = rowtotal(worksafety systemconstruction staffprotection shareholdersprotection ///
>                        publicrelations gri environmentprotection deliveryprotection ///
>                        deficiency customerprotection creditorprotection certification)

. 
end of do-file

. save "/Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta", replace
file /Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta saved

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        49902             0
        2 |            2             1
--------------------------------------

. duplicates drop stkcd year, force

Duplicates in terms of stkcd year

(1 observation deleted)

. save "/Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta", replace
file /Users/<USER>/Desktop/clitansz/原始dta数据集/社会责任.dta saved

. merge 1:1 stkcd year using "/Users/<USER>/Desktop/clitansz/合并中数据集/碳强度控制融资.dta"
(variable year was int, now float to accommodate using data's values)

    Result                      Number of obs
    -----------------------------------------
    Not matched                        22,866
        from master                    22,807  (_merge==1)
        from using                         59  (_merge==2)

    Matched                            27,096  (_merge==3)
    -----------------------------------------

. drop if _merge==1
(22,807 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta"
file /Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta saved










. use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta"

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        27155             0
--------------------------------------

. sort stkcd year

. merge m:1 stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/上市公司所处城市.
> dta"

    Result                      Number of obs
    -----------------------------------------
    Not matched                        41,696
        from master                         0  (_merge==1)
        from using                     41,696  (_merge==2)

    Matched                            27,155  (_merge==3)
    -----------------------------------------

. 
. keep if _merge==3
(41,696 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta", replace
file /Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta saved

. 
. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         1758             0
        2 |         1472           736
        3 |         1200           800
        4 |         1016           762
        5 |         1075           860
        6 |          882           735
        7 |          560           480
        8 |          576           504
        9 |          504           448
       10 |          520           468
       11 |          517           470
       12 |          456           418
       13 |          507           468
       14 |          560           520
       15 |          660           616
       16 |          560           525
       17 |          357           336
       18 |          576           544
       19 |          133           126
       20 |          400           380
       21 |          315           300
       22 |          418           399
       23 |          230           220
       24 |          312           299
       25 |          200           192
       26 |          234           225
       27 |           54            52
       28 |          280           270
       29 |          319           308
       30 |          180           174
       31 |          217           210
       32 |          256           248
       33 |          198           192
       34 |          170           165
       35 |          245           238
       36 |          180           175
       37 |          259           252
       38 |          152           148
       39 |          117           114
       40 |          200           195
       41 |          164           160
       42 |          168           164
       43 |          215           210
       44 |           44            43
       45 |          270           264
       46 |          138           135
       47 |           47            46
       48 |           48            47
       49 |          196           192
       50 |          100            98
       51 |           51            50
       53 |           53            52
       54 |           54            53
       55 |           55            54
       56 |           56            55
       57 |           57            56
       58 |           58            57
       60 |           60            59
       61 |           61            60
       64 |           64            63
       65 |           65            64
       66 |           66            65
       67 |           67            66
       68 |           68            67
       69 |          207           204
       70 |           70            69
       71 |           71            70
       73 |           73            72
       74 |           74            73
       77 |          154           152
       78 |           78            77
       84 |          168           166
       85 |           85            84
       87 |          261           258
       90 |           90            89
       91 |           91            90
       92 |          184           182
       94 |           94            93
       95 |           95            94
       98 |           98            97
      100 |          300           297
      105 |          105           104
      112 |          112           111
      113 |          113           112
      117 |          117           116
      119 |          119           118
      122 |          122           121
      125 |          125           124
      126 |          126           125
      128 |          128           127
      131 |          131           130
      137 |          137           136
      144 |          144           143
      150 |          300           298
      155 |          155           154
      160 |          160           159
      170 |          170           169
      173 |          173           172
      177 |          177           176
      193 |          193           192
      198 |          198           197
      206 |          206           205
      225 |          225           224
      226 |          226           225
      250 |          250           249
--------------------------------------






  ___  ____  ____  ____  ____ ®
 /__    /   ____/   /   ____/      18.0
___/   /   /___/   /   /___/       MP—Parallel Edition

 Statistics and Data Science       Copyright 1985-2023 StataCorp LLC
                                   StataCorp
                                   4905 Lakeway Drive
                                   College Station, Texas 77845 USA
                                   800-STATA-PC        https://www.stata.c
> om
                                   979-696-4600        <EMAIL>

Stata license: Single-user 2-core  perpetual
Serial number: 501806366048
  Licensed to: 蔡炫宇
               1

Notes:
      1. Unicode is supported; see help unicode_advice.
      2. More than 2 billion observations are allowed; see help
          obs_advice.
      3. Maximum number of variables is set to 5,000 but can be
          increased; see help set_maxvar.

Running /Applications/Stata/profile.do ...

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta"

. use "/Users/<USER>/Desktop/clitansz/原始dta数据集/上市公司所处城市.dta"
(CSMAR)

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        68851             0
--------------------------------------

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta"

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        27155             0
--------------------------------------

. merge stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/上市公
> 司所处城市.dta"
(you are using old merge syntax; see [D] merge for new syntax)
master data not sorted
r(5);

. sort stkcd year

. merge m:1 stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/上
> 市公司所处城市.dta"

    Result                      Number of obs
    -----------------------------------------
    Not matched                        41,696
        from master                         0  (_merge==1)
        from using                     41,696  (_merge==2)

    Matched                            27,155  (_merge==3)
    -----------------------------------------

. keep if _merge==3
(41,696 observations deleted)

. drop if _merge
(27,155 observations deleted)

. save "/Users/<USER>/Desktop/clipubtan/碳控制融资专利责任城市.dta"
(dataset contains 0 observations)
file /Users/<USER>/Desktop/clipubtan/碳控制融资专利责任城市.dta saved

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dt
> a"
(dataset contains 0 observations)
file /Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta
    saved

. duplicates report CITYCODE year
no observations
r(2000);

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta
> "

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任.dta"

. duplicates report stkcd year

Duplicates in terms of stkcd year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        27155             0
--------------------------------------

. sort stkcd year

. merge m:1 stkcd year using "/Users/<USER>/Desktop/clitansz/原始dta数据集/上市公司所处城市.
> dta"

    Result                      Number of obs
    -----------------------------------------
    Not matched                        41,696
        from master                         0  (_merge==1)
        from using                     41,696  (_merge==2)

    Matched                            27,155  (_merge==3)
    -----------------------------------------

. 
. keep if _merge==3
(41,696 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta", replace
file /Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta saved

. 
. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         1758             0
        2 |         1472           736
        3 |         1200           800
        4 |         1016           762
        5 |         1075           860
        6 |          882           735
        7 |          560           480
        8 |          576           504
        9 |          504           448
       10 |          520           468
       11 |          517           470
       12 |          456           418
       13 |          507           468
       14 |          560           520
       15 |          660           616
       16 |          560           525
       17 |          357           336
       18 |          576           544
       19 |          133           126
       20 |          400           380
       21 |          315           300
       22 |          418           399
       23 |          230           220
       24 |          312           299
       25 |          200           192
       26 |          234           225
       27 |           54            52
       28 |          280           270
       29 |          319           308
       30 |          180           174
       31 |          217           210
       32 |          256           248
       33 |          198           192
       34 |          170           165
       35 |          245           238
       36 |          180           175
       37 |          259           252
       38 |          152           148
       39 |          117           114
       40 |          200           195
       41 |          164           160
       42 |          168           164
       43 |          215           210
       44 |           44            43
       45 |          270           264
       46 |          138           135
       47 |           47            46
       48 |           48            47
       49 |          196           192
       50 |          100            98
       51 |           51            50
       53 |           53            52
       54 |           54            53
       55 |           55            54
       56 |           56            55
       57 |           57            56
       58 |           58            57
       60 |           60            59
       61 |           61            60
       64 |           64            63
       65 |           65            64
       66 |           66            65
       67 |           67            66
       68 |           68            67
       69 |          207           204
       70 |           70            69
       71 |           71            70
       73 |           73            72
       74 |           74            73
       77 |          154           152
       78 |           78            77
       84 |          168           166
       85 |           85            84
       87 |          261           258
       90 |           90            89
       91 |           91            90
       92 |          184           182
       94 |           94            93
       95 |           95            94
       98 |           98            97
      100 |          300           297
      105 |          105           104
      112 |          112           111
      113 |          113           112
      117 |          117           116
      119 |          119           118
      122 |          122           121
      125 |          125           124
      126 |          126           125
      128 |          128           127
      131 |          131           130
      137 |          137           136
      144 |          144           143
      150 |          300           298
      155 |          155           154
      160 |          160           159
      170 |          170           169
      173 |          173           172
      177 |          177           176
      193 |          193           192
      198 |          198           197
      206 |          206           205
      225 |          225           224
      226 |          226           225
      250 |          250           249
--------------------------------------

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta"

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         7565             0
        2 |          174            87
        3 |           72            48
--------------------------------------

. rename 地区 city
variable city already defined
r(110);

. rename 地区 CITY

. duplicates report CITYCODE CITY year

Duplicates in terms of CITYCODE CITY year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         7811             0
--------------------------------------

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta", replace
file /Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta saved

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta"

. duplicates report CITYCODE CITY year

Duplicates in terms of CITYCODE CITY year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |         1777             0
        2 |         1468           734
        3 |         1185           790
        4 |         1016           762
        5 |         1075           860
        6 |          882           735
        7 |          560           480
        8 |          576           504
        9 |          504           448
       10 |          520           468
       11 |          517           470
       12 |          456           418
       13 |          507           468
       14 |          560           520
       15 |          660           616
       16 |          560           525
       17 |          357           336
       18 |          576           544
       19 |          133           126
       20 |          400           380
       21 |          315           300
       22 |          418           399
       23 |          230           220
       24 |          312           299
       25 |          200           192
       26 |          234           225
       27 |           54            52
       28 |          280           270
       29 |          319           308
       30 |          180           174
       31 |          217           210
       32 |          256           248
       33 |          198           192
       34 |          170           165
       35 |          245           238
       36 |          180           175
       37 |          259           252
       38 |          152           148
       39 |          117           114
       40 |          200           195
       41 |          164           160
       42 |          168           164
       43 |          215           210
       44 |           44            43
       45 |          270           264
       46 |          138           135
       47 |           47            46
       48 |           48            47
       49 |          196           192
       50 |          100            98
       51 |           51            50
       53 |           53            52
       54 |           54            53
       55 |           55            54
       56 |           56            55
       57 |           57            56
       58 |           58            57
       60 |           60            59
       61 |           61            60
       64 |           64            63
       65 |           65            64
       66 |           66            65
       67 |           67            66
       68 |           68            67
       69 |          207           204
       70 |           70            69
       71 |           71            70
       73 |           73            72
       74 |           74            73
       77 |          154           152
       78 |           78            77
       84 |          168           166
       85 |           85            84
       87 |          261           258
       90 |           90            89
       91 |           91            90
       92 |          184           182
       94 |           94            93
       95 |           95            94
       98 |           98            97
      100 |          300           297
      105 |          105           104
      112 |          112           111
      113 |          113           112
      117 |          117           116
      119 |          119           118
      122 |          122           121
      125 |          125           124
      126 |          126           125
      128 |          128           127
      131 |          131           130
      137 |          137           136
      144 |          144           143
      150 |          300           298
      155 |          155           154
      160 |          160           159
      170 |          170           169
      173 |          173           172
      177 |          177           176
      193 |          193           192
      198 |          198           197
      206 |          206           205
      225 |          225           224
      226 |          226           225
      250 |          250           249
--------------------------------------

. merge m:1 CITYCODE CITY year using  "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dt
> a"
key variable CITYCODE is str6 in master but long in using data
    Each key variable (on which observations are matched) must be of the same generic
    type in the master and using datasets.  Same generic type means both numeric or
    both string.
r(106);

. destring CITYCODE, replace
CITYCODE: contains nonnumeric characters; no replace

. merge m:1 CITYCODE CITY year using  "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dt
> a"
key variable CITYCODE is str6 in master but long in using data
    Each key variable (on which observations are matched) must be of the same generic
    type in the master and using datasets.  Same generic type means both numeric or
    both string.
r(106);

. 
. replace CITYCODE = "420600" if inlist(CITY, "襄阳市")
(0 real changes made)

. replace CITYCODE = "540400" if inlist(CITY, "林芝市")
(5 real changes made)

. replace CITYCODE = "320685" if inlist(CITY, "海安市")
(8 real changes made)

. replace CITYCODE = "430182" if inlist(CITY, "宁乡市")
(4 real changes made)

. replace CITYCODE = "630200" if inlist(CITY, "海东市")
(2 real changes made)

. replace CITYCODE = "331083" if inlist(CITY, "玉环市")
(1 real change made)

. drop if CITYCODE == "None"
(15 observations deleted)

. destring CITYCODE, replace
CITYCODE: all characters numeric; replaced as long

. merge m:1 CITYCODE CITY year using "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta
> "
variable _merge already defined
r(110);

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta", replace
file /Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta saved

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta"

. drop if _merge == 2
(1,623 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta", replace
file /Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta saved

. use "/Users/<USER>/Desktop/clitansz/合并中数据集/碳控制融资专利责任城市.dta"

. merge m:1 CITYCODE CITY year using "/Users/<USER>/Desktop/clitansz/合并中数据集/城市级.dta
> "

    Result                      Number of obs
    -----------------------------------------
    Not matched                         8,258
        from master                     4,683  (_merge==1)
        from using                      3,575  (_merge==2)

    Matched                            22,457  (_merge==3)
    -----------------------------------------

. 
. keep if _merge==3
(8,258 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/alldata.dta"
file /Users/<USER>/Desktop/clitansz/alldata.dta saved

. sort stkcd year

. 
. keep if any_st == 0
(100 observations deleted)

. drop is_st any_st

. save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace
file /Users/<USER>/Desktop/clitansz/alldata.dta saved

. gen is_bank = strpos(STKNM, "银行") > 0

. list stkcd STKNM if is_bank

. drop is_bank

. egen n_stkcd = tag(stkcd)

. count if n_stkcd==1
  3,203

. gen is_b = strpos(STKNM, "B") > 0

. bysort stkcd: egen any_b = max(is_b)

. keep if any_b == 0
(0 observations deleted)

. drop is_b any_b

. keep if year >= 2011 & year <= 2023
(0 observations deleted)

. save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace
file /Users/<USER>/Desktop/clitansz/alldata.dta saved

. drop if MARKET == "北证A股"
(191 observations deleted)

. save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace
file /Users/<USER>/Desktop/clitansz/alldata.dta saved

. count if n_stkcd==1
  3,084

. save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace
file /Users/<USER>/Desktop/clitansz/alldata.dta saved

. rename 绿色金融改革创新试验区 grfin

. rename KZ指数 KZ

. rename FC指数 FC

. rename SA指数 SA

. rename SA指数_abs SA_abs

. rename WW指数 WW

. save "/Users/<USER>/Desktop/clitansz/alldata.dta", replace
file /Users/<USER>/Desktop/clitansz/alldata.dta saved



  ___  ____  ____  ____  ____ ®
 /__    /   ____/   /   ____/      18.0
___/   /   /___/   /   /___/       MP—Parallel Edition

 Statistics and Data Science       Copyright 1985-2023 StataCorp LLC
                                   StataCorp
                                   4905 Lakeway Drive
                                   College Station, Texas 77845 USA
                                   800-STATA-PC        https://www.stata.com
                                   979-696-4600        <EMAIL>

Stata license: Single-user 2-core  perpetual
Serial number: 501806366048
  Licensed to: 蔡炫宇
               1

Notes:
      1. Unicode is supported; see help unicode_advice.
      2. More than 2 billion observations are allowed; see help obs_advice.
      3. Maximum number of variables is set to 5,000 but can be increased; see help set_maxvar.

Running /Applications/Stata/profile.do ...

. 
. 
. import delimited "/Users/<USER>/Desktop/qhbqdx/中国城市控制变量2023（未插值）.csv"
(encoding automatically selected: UTF-8)
(214 vars, 10,200 obs)

. rename 城市代码 CITYCODE

. rename 年份 year

. duplicates report CITYCODE

Duplicates in terms of CITYCODE

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
       34 |        10200          9900
--------------------------------------

. duplicates report CITYCODE year

Duplicates in terms of CITYCODE year

--------------------------------------
   Copies | Observations       Surplus
----------+---------------------------
        1 |        10200             0
--------------------------------------

. merge 1:m CITYCODE year using "/Users/<USER>/Desktop/clitansz/alldata.dt
> a"
(variable year was int, now float to accommodate using data's values)

    Result                      Number of obs
    -----------------------------------------
    Not matched                         7,591
        from master                     7,591  (_merge==1)
        from using                          0  (_merge==2)

    Matched                            22,166  (_merge==3)
    -----------------------------------------

. keep if _merge==3
(7,591 observations deleted)

. drop _merge

. save "/Users/<USER>/Desktop/clitansz/alldata2.dta"
file /Users/<USER>/Desktop/clitansz/alldata2.dta saved

. 
